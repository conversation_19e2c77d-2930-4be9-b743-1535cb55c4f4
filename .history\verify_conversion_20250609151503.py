#!/usr/bin/env python3
"""
验证Elasticsearch字段类型转换结果的脚本
"""

from elasticsearch import Elasticsearch
import json


def verify_field_conversion():
    """验证字段类型转换结果"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    FIELD_NAME = "disk_lsof_tmp_deletey"
    
    print(f"验证ES索引 {INDEX_NAME} 中字段 {FIELD_NAME} 的转换结果...")
    print("=" * 60)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查索引是否存在
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return False
        
        print(f"✓ 索引 {INDEX_NAME} 存在")
        
        # 2. 获取索引信息
        try:
            # 检查是否是别名
            aliases = es.indices.get_alias(name=INDEX_NAME)
            if aliases:
                print(f"✓ {INDEX_NAME} 是别名，指向索引: {list(aliases.keys())}")
                actual_indices = list(aliases.keys())
            else:
                actual_indices = [INDEX_NAME]
                print(f"✓ {INDEX_NAME} 是直接索引")
        except:
            actual_indices = [INDEX_NAME]
        
        # 3. 获取映射信息
        mapping_info = es.indices.get_mapping(index=INDEX_NAME)
        
        for actual_index in actual_indices:
            if actual_index in mapping_info:
                mappings = mapping_info[actual_index]['mappings']
                
                print(f"\n索引 {actual_index} 的映射信息:")
                
                if 'properties' in mappings:
                    if FIELD_NAME in mappings['properties']:
                        field_config = mappings['properties'][FIELD_NAME]
                        field_type = field_config.get('type', '未知')
                        null_value = field_config.get('null_value', '未设置')
                        
                        print(f"✓ 字段 {FIELD_NAME}:")
                        print(f"  - 类型: {field_type}")
                        print(f"  - 默认值: {null_value}")
                        
                        if field_type == 'integer':
                            print("✅ 字段类型转换成功！")
                        else:
                            print(f"⚠ 字段类型仍为 {field_type}，转换可能未完成")
                    else:
                        print(f"⚠ 字段 {FIELD_NAME} 不存在于映射中")
                        print("可用字段:", list(mappings['properties'].keys())[:10])
                else:
                    print("⚠ 索引没有properties映射")
        
        # 4. 获取样本数据验证
        print(f"\n获取样本数据验证...")
        try:
            search_body = {
                "size": 10,
                "_source": [FIELD_NAME],
                "query": {"match_all": {}}
            }
            
            response = es.search(index=INDEX_NAME, body=search_body)
            total_docs = response['hits']['total']
            
            if isinstance(total_docs, dict):
                total_count = total_docs['value']
            else:
                total_count = total_docs
                
            print(f"✓ 索引总文档数: {total_count}")
            print(f"✓ 样本数据 (前10条):")
            
            for i, hit in enumerate(response['hits']['hits'], 1):
                source = hit['_source']
                field_value = source.get(FIELD_NAME, "字段不存在")
                value_type = type(field_value).__name__
                
                print(f"  {i}. 文档ID: {hit['_id']}")
                print(f"     {FIELD_NAME}: {field_value} (Python类型: {value_type})")
                
                # 验证数据类型
                if isinstance(field_value, int):
                    print(f"     ✅ 数据类型正确 (integer)")
                elif field_value == "字段不存在":
                    print(f"     ⚠ 字段在文档中不存在")
                else:
                    print(f"     ❌ 数据类型不正确，期望integer，实际为{value_type}")
                print()
                
        except Exception as search_error:
            print(f"❌ 搜索样本数据失败: {search_error}")
        
        # 5. 统计字段值分布
        print("统计字段值分布...")
        try:
            agg_body = {
                "size": 0,
                "aggs": {
                    "field_values": {
                        "terms": {
                            "field": FIELD_NAME,
                            "size": 10
                        }
                    },
                    "field_stats": {
                        "stats": {
                            "field": FIELD_NAME
                        }
                    }
                }
            }
            
            agg_response = es.search(index=INDEX_NAME, body=agg_body)
            
            if 'aggregations' in agg_response:
                # 值分布
                if 'field_values' in agg_response['aggregations']:
                    buckets = agg_response['aggregations']['field_values']['buckets']
                    print("✓ 字段值分布 (前10个):")
                    for bucket in buckets:
                        print(f"  值: {bucket['key']} -> 文档数: {bucket['doc_count']}")
                
                # 统计信息
                if 'field_stats' in agg_response['aggregations']:
                    stats = agg_response['aggregations']['field_stats']
                    if stats['count'] > 0:
                        print(f"✓ 字段统计信息:")
                        print(f"  - 有值文档数: {stats['count']}")
                        print(f"  - 最小值: {stats['min']}")
                        print(f"  - 最大值: {stats['max']}")
                        print(f"  - 平均值: {stats['avg']:.2f}")
                    else:
                        print("⚠ 没有找到有效的字段值")
                        
        except Exception as agg_error:
            print(f"⚠ 聚合查询失败: {agg_error}")
        
        print("=" * 60)
        print("✅ 验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        return False


if __name__ == "__main__":
    verify_field_conversion()
