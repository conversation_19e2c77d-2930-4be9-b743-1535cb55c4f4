#!/usr/bin/env python3
"""
整理项目文件结构
"""

import os
import shutil
from pathlib import Path


def organize_files():
    """整理文件到不同目录"""
    
    # 创建目录结构
    directories = {
        'core': '核心功能脚本',
        'utils': '工具和辅助脚本', 
        'verify': '验证脚本',
        'archive': '历史版本和废弃脚本'
    }
    
    print("创建目录结构...")
    for dir_name, description in directories.items():
        os.makedirs(dir_name, exist_ok=True)
        print(f"✓ 创建目录: {dir_name}/ - {description}")
    
    # 文件分类映射
    file_mapping = {
        # 核心功能脚本 - 主要使用的脚本
        'core': [
            'delete_fields_config.py',      # 主要的字段删除脚本
            'es_field_type_converter.py',   # 字段类型转换脚本
        ],
        
        # 工具脚本 - 辅助功能
        'utils': [
            'check_fields.py',              # 检查字段信息
            'check_index_status.py',        # 检查索引状态
            'rename_index_final.py',        # 索引重命名
        ],
        
        # 验证脚本 - 验证操作结果
        'verify': [
            'verify_conversion.py',         # 验证字段转换
            'verify_deleted_fields.py',     # 验证字段删除
            'verify_new_deleted_fields.py', # 验证新删除的字段
        ],
        
        # 历史版本 - 旧版本和实验性脚本
        'archive': [
            'delete_fields_complete.py',    # 完整版字段删除（被config版本替代）
            'delete_fields.py',             # 早期版本
            'delete_fields_safe.py',        # 安全版本（被complete版本替代）
            'simple_es_converter.py',       # 简化版转换器
            'convert_disk_lsof_tmp_delete.py', # 特定字段转换
            'rename_and_delete_fields.py',  # 重命名和删除组合
            'rename_index.py',              # 索引重命名早期版本
        ]
    }
    
    print("\n移动文件...")
    moved_files = 0
    
    for target_dir, files in file_mapping.items():
        for filename in files:
            if os.path.exists(filename):
                target_path = os.path.join(target_dir, filename)
                shutil.move(filename, target_path)
                print(f"✓ 移动: {filename} -> {target_dir}/")
                moved_files += 1
            else:
                print(f"⚠ 文件不存在: {filename}")
    
    print(f"\n✅ 文件整理完成！共移动了 {moved_files} 个文件")
    
    # 创建各目录的说明文件
    create_directory_readme()


def create_directory_readme():
    """为每个目录创建README文件"""
    
    readme_contents = {
        'core': """# 核心功能脚本

这个目录包含主要的功能脚本，是日常使用的核心工具。

## 文件说明

### delete_fields_config.py 🌟 **主要使用**
- **功能**: 删除Elasticsearch索引中的指定字段
- **特点**: 可配置，只需修改字段列表即可使用
- **使用**: `python delete_fields_config.py`

### es_field_type_converter.py
- **功能**: 转换字段类型（如text转integer）
- **特点**: 完整的类型转换功能，包含数据转换逻辑
- **使用**: `python es_field_type_converter.py`

## 使用建议

1. **删除字段**: 优先使用 `delete_fields_config.py`
2. **类型转换**: 使用 `es_field_type_converter.py`
3. **操作前**: 建议先运行验证脚本检查状态
""",
        
        'utils': """# 工具和辅助脚本

这个目录包含各种工具脚本，用于检查、诊断和维护操作。

## 文件说明

### check_fields.py
- **功能**: 检查索引中字段的详细信息
- **用途**: 分析字段类型、使用情况等
- **使用**: `python check_fields.py`

### check_index_status.py
- **功能**: 检查和修复索引状态
- **用途**: 解决索引别名问题，清理临时索引
- **使用**: `python check_index_status.py`

### rename_index_final.py
- **功能**: 将索引重命名为最终名称
- **用途**: 将临时索引名改回正式名称
- **使用**: `python rename_index_final.py`

## 使用场景

- **问题诊断**: 使用check_*脚本
- **状态修复**: 索引名称或别名问题
- **信息查询**: 了解字段和索引详情
""",
        
        'verify': """# 验证脚本

这个目录包含用于验证操作结果的脚本。

## 文件说明

### verify_conversion.py
- **功能**: 验证字段类型转换结果
- **检查**: 字段类型、默认值、数据完整性
- **使用**: `python verify_conversion.py`

### verify_deleted_fields.py
- **功能**: 验证字段删除结果
- **检查**: 字段是否从映射和数据中删除
- **使用**: `python verify_deleted_fields.py`

### verify_new_deleted_fields.py
- **功能**: 验证最新删除的字段
- **检查**: 针对特定字段列表的验证
- **使用**: `python verify_new_deleted_fields.py`

## 使用建议

1. **操作后验证**: 每次重要操作后运行相应验证脚本
2. **定期检查**: 定期验证索引状态
3. **问题排查**: 出现问题时用于诊断
""",
        
        'archive': """# 历史版本和废弃脚本

这个目录包含旧版本、实验性脚本和已被替代的脚本。

## 文件说明

### delete_fields_complete.py
- **状态**: 已被 `core/delete_fields_config.py` 替代
- **功能**: 完整的字段删除功能
- **说明**: 面向对象设计，功能完整但配置复杂

### delete_fields.py / delete_fields_safe.py
- **状态**: 早期版本，已废弃
- **功能**: 字段删除的早期实现
- **说明**: 功能不完整，建议使用新版本

### simple_es_converter.py
- **状态**: 简化版本
- **功能**: 简单的字段类型转换
- **说明**: 功能有限，建议使用完整版

### 其他脚本
- **convert_disk_lsof_tmp_delete.py**: 特定字段的转换脚本
- **rename_and_delete_fields.py**: 组合操作脚本
- **rename_index.py**: 索引重命名早期版本

## 注意事项

⚠️ 这些脚本仅供参考，不建议在生产环境使用。
✅ 请使用 `core/` 目录中的最新版本。
"""
    }
    
    print("\n创建目录说明文件...")
    for dir_name, content in readme_contents.items():
        readme_path = os.path.join(dir_name, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 创建: {dir_name}/README.md")


if __name__ == "__main__":
    organize_files()
