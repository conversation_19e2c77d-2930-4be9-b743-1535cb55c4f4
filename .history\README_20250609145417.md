# Elasticsearch字段类型转换脚本

这个项目包含用于将Elasticsearch索引中字段类型从text转换为integer的Python脚本。

## 目标

将ES服务器 `http://***************/` 中的 `psis-collector-harddisk-index` 索引里的 `disk_lsof_tmp_deletey` 字段从 `text` 类型转换为 `integer` 类型，默认值设为 0。

## 文件说明

1. **es_field_type_converter.py** - 完整功能的转换脚本，包含详细的错误处理和验证
2. **simple_es_converter.py** - 简化版本的转换脚本，适合快速执行
3. **requirements.txt** - Python依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install elasticsearch
```

## 使用方法

### 方法1：使用完整版脚本（推荐）

```bash
python es_field_type_converter.py
```

这个脚本会：
- 详细显示每个步骤的进度
- 在交换索引前要求用户确认
- 提供完整的错误处理和回滚机制
- 验证转换结果

### 方法2：使用简化版脚本

```bash
python simple_es_converter.py
```

这个脚本会直接执行转换，适合自动化场景。

## 转换流程

1. **连接验证** - 测试与Elasticsearch服务器的连接
2. **获取映射** - 读取当前索引的字段映射配置
3. **创建新映射** - 修改目标字段的类型为integer，设置默认值为0
4. **创建临时索引** - 使用新映射创建临时索引
5. **重新索引** - 将原数据复制到临时索引，同时转换字段类型
6. **交换索引** - 删除原索引，将临时索引重命名为原索引名
7. **验证结果** - 检查转换是否成功

## 数据转换规则

- 如果原字段值可以转换为整数，则转换为对应的整数值
- 如果原字段值无法转换为整数，则设置为默认值 0
- 如果原字段值为null或不存在，则设置为默认值 0

## 注意事项

⚠️ **重要警告**：
- 此操作会删除原索引并创建新索引
- 请在执行前备份重要数据
- 建议先在测试环境中验证脚本

## 配置修改

如需修改配置，请编辑脚本中的以下变量：

```python
ES_HOST = "http://***************/"  # ES服务器地址
INDEX_NAME = "psis-collector-harddisk-index"  # 索引名称
FIELD_NAME = "disk_lsof_tmp_deletey"  # 要转换的字段名
```

## 故障排除

1. **连接失败**：检查ES服务器地址和网络连接
2. **索引不存在**：确认索引名称是否正确
3. **权限不足**：确保有足够的权限进行索引操作
4. **内存不足**：对于大型索引，可能需要调整ES的内存设置

## 验证转换结果

转换完成后，可以使用以下命令验证：

```bash
curl -X GET "http://***************/psis-collector-harddisk-index/_mapping"
```

查看字段类型是否已更改为integer。
