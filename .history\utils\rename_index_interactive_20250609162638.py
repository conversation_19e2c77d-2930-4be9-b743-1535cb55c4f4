#!/usr/bin/env python3
"""
交互式Elasticsearch索引重命名脚本
支持用户输入任意的当前索引名和目标索引名
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


def get_user_input():
    """获取用户输入的索引名称"""
    print("📝 请输入索引信息:")
    print("-" * 40)
    
    # 获取当前索引名
    while True:
        current_index = input("当前索引名称: ").strip()
        if current_index:
            break
        print("❌ 索引名称不能为空，请重新输入")
    
    # 获取目标索引名
    while True:
        target_index = input("目标索引名称: ").strip()
        if target_index:
            break
        print("❌ 索引名称不能为空，请重新输入")
    
    # 确认信息
    print(f"\n📋 确认信息:")
    print(f"  当前索引: {current_index}")
    print(f"  目标索引: {target_index}")
    
    if current_index == target_index:
        print("❌ 当前索引名和目标索引名相同，无需重命名")
        return None, None
    
    confirm = input("\n是否确认以上信息？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消")
        return None, None
    
    return current_index, target_index


def rename_index_interactive():
    """交互式索引重命名"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    
    print("🔄 Elasticsearch索引重命名工具")
    print("=" * 60)
    
    # 获取用户输入
    current_index, target_index = get_user_input()
    if not current_index or not target_index:
        return False
    
    temp_index = f"{target_index}_temp_{int(time.time())}"
    
    print(f"\n索引重命名操作:")
    print(f"  当前索引: {current_index}")
    print(f"  目标索引: {target_index}")
    print(f"  临时索引: {temp_index}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查当前状态
        print("\n1. 检查索引状态...")
        
        # 检查当前索引是否存在
        if not es.indices.exists(index=current_index):
            print(f"❌ 当前索引 {current_index} 不存在")
            return False
        
        print(f"✓ 当前索引 {current_index} 存在")
        
        # 检查目标索引是否已存在
        target_exists = es.indices.exists(index=target_index)
        if target_exists:
            # 检查是否是别名
            try:
                target_aliases = es.indices.get_alias(name=target_index)
                if target_aliases:
                    print(f"⚠ 目标索引名 {target_index} 当前是别名，将被替换")
                else:
                    print(f"❌ 目标索引名 {target_index} 已被实际索引占用")
                    user_continue = input("是否继续？这将删除现有索引 (y/N): ")
                    if user_continue.lower() != 'y':
                        return False
            except:
                print(f"❌ 目标索引名 {target_index} 已被占用")
                user_continue = input("是否继续？这将删除现有索引 (y/N): ")
                if user_continue.lower() != 'y':
                    return False
        else:
            print(f"✓ 目标索引名 {target_index} 可用")
        
        # 2. 获取当前索引的设置和映射
        print("\n2. 获取索引设置和映射...")
        
        # 获取设置
        settings_response = es.indices.get_settings(index=current_index)
        current_settings = settings_response[current_index]['settings']['index']
        
        # 清理设置
        clean_settings = {}
        for key, value in current_settings.items():
            if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                clean_settings[key] = value
        
        # 获取映射
        mapping_response = es.indices.get_mapping(index=current_index)
        current_mapping = mapping_response[current_index]['mappings']
        
        print("✓ 成功获取索引设置和映射")
        
        # 3. 删除目标索引（如果存在）
        if target_exists:
            print(f"\n3. 删除现有目标索引...")
            try:
                # 先删除别名
                target_aliases = es.indices.get_alias(name=target_index)
                if target_aliases:
                    for existing_index in target_aliases.keys():
                        es.indices.delete_alias(index=existing_index, name=target_index)
                        print(f"✓ 已删除别名 {target_index}")
            except:
                pass
            
            # 删除索引
            es.indices.delete(index=target_index)
            print(f"✓ 已删除现有索引 {target_index}")
        
        # 4. 创建新索引
        print(f"\n4. 创建新索引 {target_index}...")
        
        create_body = {
            "settings": clean_settings,
            "mappings": current_mapping
        }
        
        es.indices.create(index=target_index, body=create_body)
        print(f"✓ 新索引 {target_index} 创建成功")
        
        # 5. 重新索引数据
        print("\n5. 开始数据迁移...")
        
        reindex_body = {
            "source": {"index": current_index},
            "dest": {"index": target_index}
        }
        
        # 启动异步重新索引
        result = es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
        task_id = result['task']
        print(f"✓ 数据迁移任务已启动，任务ID: {task_id}")
        
        # 监控重新索引进度
        print("监控数据迁移进度...")
        while True:
            try:
                task_status = es.tasks.get(task_id=task_id)
                
                if task_status['completed']:
                    print("✓ 数据迁移任务完成")
                    response = task_status.get('response', {})
                    print(f"  处理文档数: {response.get('total', 0)}")
                    print(f"  创建文档数: {response.get('created', 0)}")
                    if response.get('failures'):
                        print(f"  失败记录: {len(response['failures'])}")
                    break
                else:
                    task_info = task_status['task']
                    status = task_info.get('status', {})
                    total = status.get('total', 0)
                    created = status.get('created', 0)
                    if total > 0:
                        progress = (created / total) * 100
                        print(f"  进度: {created}/{total} ({progress:.1f}%)")
                    else:
                        print(f"  已处理: {created} 条记录")
                    
                    time.sleep(5)
                    
            except Exception as task_error:
                print(f"⚠ 获取任务状态失败: {task_error}")
                time.sleep(5)
        
        # 6. 验证新索引
        print("\n6. 验证新索引...")
        
        # 检查文档数量
        old_count = es.count(index=current_index)['count']
        new_count = es.count(index=target_index)['count']
        
        print(f"  原索引文档数: {old_count}")
        print(f"  新索引文档数: {new_count}")
        
        if old_count == new_count:
            print("✅ 文档数量验证通过")
        else:
            print(f"⚠ 文档数量不匹配，差异: {old_count - new_count}")
            user_continue = input("文档数量不匹配，是否继续？(y/N): ")
            if user_continue.lower() != 'y':
                print("操作已取消，正在清理...")
                es.indices.delete(index=target_index)
                return False
        
        # 检查样本数据
        search_body = {
            "size": 3,
            "_source": True,
            "query": {"match_all": {}}
        }
        
        try:
            old_response = es.search(index=current_index, body=search_body)
            new_response = es.search(index=target_index, body=search_body)
            
            print("  样本数据验证:")
            for i, (old_hit, new_hit) in enumerate(zip(old_response['hits']['hits'], new_response['hits']['hits']), 1):
                old_id = old_hit['_id']
                new_id = new_hit['_id']
                print(f"    {i}. 文档ID: {old_id} -> {new_id}")
                
        except Exception as search_error:
            print(f"⚠ 样本数据验证失败: {search_error}")
        
        # 7. 确认删除旧索引
        print("\n" + "=" * 60)
        user_input = input("验证通过！是否删除旧索引？(y/N): ")
        
        if user_input.lower() != 'y':
            print("操作已取消")
            print(f"新索引 {target_index} 已创建，旧索引 {current_index} 保留")
            return True
        
        # 8. 删除旧索引
        print("\n7. 删除旧索引...")
        es.indices.delete(index=current_index)
        print(f"✓ 已删除旧索引 {current_index}")
        
        # 9. 最终验证
        print("\n8. 最终验证...")
        
        if es.indices.exists(index=target_index):
            print(f"✅ 新索引 {target_index} 存在")
            
            # 检查索引状态
            final_count = es.count(index=target_index)['count']
            print(f"✅ 最终文档数: {final_count}")
            
            # 检查字段映射
            final_mapping = es.indices.get_mapping(index=target_index)
            properties = final_mapping[target_index]['mappings']['properties']
            print(f"✅ 总字段数: {len(properties)}")
        else:
            print(f"❌ 新索引 {target_index} 不存在")
            return False
        
        print("=" * 60)
        print("✅ 索引重命名完成！")
        print(f"索引名已从 {current_index} 改为 {target_index}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        
        # 清理可能创建的新索引
        try:
            if es.indices.exists(index=target_index):
                # 检查是否是我们刚创建的索引
                creation_time = es.indices.get_settings(index=target_index)[target_index]['settings']['index']['creation_date']
                current_time = int(time.time() * 1000)
                if current_time - int(creation_time) < 3600000:  # 1小时内创建的
                    es.indices.delete(index=target_index)
                    print(f"已清理新创建的索引 {target_index}")
        except:
            pass
        
        return False


def main():
    """主函数"""
    try:
        success = rename_index_interactive()
        if success:
            print("\n🎉 操作成功完成！")
            return 0
        else:
            print("\n❌ 操作失败或被取消")
            return 1
    except KeyboardInterrupt:
        print("\n\n👋 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
