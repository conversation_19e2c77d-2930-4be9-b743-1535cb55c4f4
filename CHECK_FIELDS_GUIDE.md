# 🔍 交互式字段检查使用指南

## 🎯 功能概述

新的交互式字段检查脚本 `utils/check_fields.py` 支持用户指定要检查的字段，提供详细的字段信息分析和优化建议。

## 🚀 使用方法

### 方法1：直接运行脚本
```bash
python utils/check_fields.py
```

### 方法2：通过主菜单
```bash
python main.py
# 选择 3. 🔍 检查字段信息
```

## 📝 支持的输入方式

### 1️⃣ 逐个输入字段名
适合检查少量字段时使用：

```
请选择 (0-4): 1

请逐个输入字段名（输入空行结束）:
字段名: disk_lsof_tmp_delete
✓ 已添加字段: disk_lsof_tmp_delete
字段名: IP
✓ 已添加字段: IP
字段名: [直接回车结束]
```

### 2️⃣ 批量输入字段（推荐）
支持逗号分隔的批量输入：

```
请选择 (0-4): 2

请输入要检查的字段（用逗号分隔）:
字段列表: disk_lsof_tmp_delete,IP,Date,NeName
```

**支持的格式：**
- `field1,field2,field3`
- `field1, field2, field3` （带空格）
- `field1,field2,field3,field4,field5` （任意数量）

### 3️⃣ 查看所有字段
查看索引中的所有字段列表：

```
请选择 (0-4): 3

📋 索引 psis-collector-harddisk-index 中的所有字段 (共 79 个):
  1. Date                     (类型: date)
  2. IP                       (类型: text)
  ...
 79. tmpfs_tmpUsed            (类型: text)
```

### 4️⃣ 按关键词搜索字段
搜索包含特定关键词的字段：

```
请选择 (0-4): 4

🔍 按关键词搜索字段:
输入搜索关键词: tmpfs

包含 'tmpfs' 的字段 (共 40 个):
  1. devtmpfs_devAvail        (类型: text)
  2. devtmpfs_devMounted      (类型: text)
  ...
 40. tmpfs_tmpUsed            (类型: text)

是否检查这 40 个匹配的字段？(y/N): y
```

## 📊 检查结果示例

### 字段基本信息
```
检查 4 个指定字段:
------------------------------------------------------------
字段名: disk_lsof_tmp_delete
  类型: integer
  默认值: 0

字段名: IP
  类型: text

字段名: Date
  类型: date

字段名: NeName
  类型: text
```

### 样本数据分析
```
获取样本数据检查字段值...
------------------------------------------------------------

检查字段: disk_lsof_tmp_delete
  样本数据:
    1. disk_lsof_tmp_delete: 0 (类型: int)
    2. disk_lsof_tmp_delete: 0 (类型: int)
    3. disk_lsof_tmp_delete: 0 (类型: int)
  值分布:
    值: 0 -> 文档数: 127789

检查字段: IP
  样本数据:
    1. IP: *************** (类型: str)
    2. IP: *************** (类型: str)
    3. IP: *************** (类型: str)
```

### 字段优化建议
```
字段分析建议:
------------------------------------------------------------
📊 disk_lsof_tmp_delete:
   类型: integer
   💡 建议: 适合数值计算和范围查询

📊 IP:
   类型: text
   💡 建议: 如果该字段存储数值，考虑转换为 integer 或 float 类型

📊 Date:
   类型: date
   💡 建议: 适合时间范围查询和时间聚合

📊 NeName:
   类型: text
   💡 建议: 如果该字段存储数值，考虑转换为 integer 或 float 类型
```

## 📋 操作流程示例

### 示例1：检查特定字段

```bash
python utils/check_fields.py
```

1. **选择输入方式**：
   ```
   请选择 (0-4): 2
   ```

2. **输入字段列表**：
   ```
   字段列表: disk_lsof_tmp_delete,IP,Date,NeName
   ```

3. **确认字段**：
   ```
   📋 确认要检查的字段 (共 4 个):
     1. disk_lsof_tmp_delete
     2. IP
     3. Date
     4. NeName
   
   是否确认检查以上 4 个字段？(y/N): y
   ```

4. **查看检查结果**：
   - ✅ 字段基本信息（类型、默认值等）
   - ✅ 样本数据分析（实际值和类型）
   - ✅ 值分布统计
   - ✅ 优化建议

### 示例2：搜索相关字段

```bash
python utils/check_fields.py
```

1. **选择搜索功能**：
   ```
   请选择 (0-4): 4
   ```

2. **输入搜索关键词**：
   ```
   输入搜索关键词: tmpfs
   ```

3. **查看搜索结果**：
   ```
   包含 'tmpfs' 的字段 (共 40 个):
     1. devtmpfs_devAvail
     2. devtmpfs_devMounted
     ...
   
   是否检查这 40 个匹配的字段？(y/N): y
   ```

## 🔍 检查内容详解

### 1. 字段映射信息
- **字段类型**：text, keyword, integer, float, date 等
- **默认值**：null_value 设置
- **分析器**：analyzer 配置
- **格式**：date 字段的格式设置

### 2. 样本数据分析
- **实际值**：字段在文档中的实际值
- **数据类型**：Python 中的数据类型
- **存在性**：字段是否在文档中存在

### 3. 值分布统计
- **聚合统计**：字段值的分布情况
- **文档计数**：每个值对应的文档数量
- **热门值**：最常见的字段值

### 4. 优化建议
- **类型建议**：根据字段类型提供优化建议
- **性能建议**：查询和聚合性能优化
- **存储建议**：存储空间优化

## ⚠️ 注意事项

### 字段类型限制
- **text 字段**：默认禁用 fielddata，聚合查询可能失败
- **keyword 字段**：适合精确匹配和聚合
- **数值字段**：支持范围查询和数学运算
- **日期字段**：支持时间范围查询

### 性能考虑
- **大量字段**：检查大量字段时可能较慢
- **聚合查询**：text 字段的聚合可能失败
- **样本数据**：默认只显示5个样本

## 🛠️ 故障排除

### 常见问题

#### Q: 提示字段不存在？
A: 检查字段名拼写，可以使用选项3查看所有字段。

#### Q: 聚合查询失败？
A: text 字段默认禁用 fielddata，这是正常现象。

#### Q: 如何查看更多样本？
A: 脚本默认显示5个样本，可以修改代码中的 size 参数。

#### Q: 搜索结果太多？
A: 使用更具体的搜索关键词，或选择部分字段检查。

## 📊 实用技巧

### 1. 快速定位问题字段
```bash
# 搜索特定前缀的字段
输入搜索关键词: tmpfs_runuser

# 检查数值类型字段
输入搜索关键词: Usage
```

### 2. 批量检查相关字段
```bash
# 检查所有磁盘相关字段
字段列表: disk_lsof_tmp_delete,disk_pur_dwdm_core_file,disk_var_log_large_file

# 检查基础信息字段
字段列表: IP,Date,NeName,NeType,Owner
```

### 3. 字段优化决策
- **text → keyword**：精确匹配场景
- **text → integer/float**：数值计算场景
- **添加 .keyword 子字段**：既要全文搜索又要精确匹配

## 🔗 相关工具

检查完字段后，可以使用以下工具进行优化：

- **字段类型转换**: `python core/es_field_type_converter.py`
- **字段删除**: `python core/delete_fields_config.py`
- **索引重命名**: `python utils/rename_index_interactive.py`

---

💡 **提示**: 定期检查字段信息有助于优化索引性能和存储效率！
