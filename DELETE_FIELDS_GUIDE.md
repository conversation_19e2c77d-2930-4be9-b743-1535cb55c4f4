# 🗑️ 交互式字段删除使用指南

## 🎯 功能概述

新的交互式字段删除脚本 `core/delete_fields_config.py` 支持多种灵活的字段输入方式，让您可以轻松删除不需要的字段。

## 🚀 使用方法

### 方法1：直接运行脚本
```bash
python core/delete_fields_config.py
```

### 方法2：通过主菜单
```bash
python main.py
# 选择 1. 🗑️ 字段删除操作
# 选择 1. 配置并删除字段 (推荐)
```

## 📝 支持的输入方式

### 1️⃣ 逐个输入字段名
适合删除少量字段时使用：

```
请选择 (0-3): 1

请逐个输入字段名（输入空行结束）:
字段名: tmpfs_runuser1009Used
✓ 已添加字段: tmpfs_runuser1009Used
字段名: tmpfs_runuser1009Usage
✓ 已添加字段: tmpfs_runuser1009Usage
字段名: [直接回车结束]
```

### 2️⃣ 批量输入字段（推荐）
支持逗号分隔的批量输入：

```
请选择 (0-3): 2

请输入要删除的字段（用逗号分隔）:
字段列表: tmpfs_runuser1009Used,tmpfs_runuser1009Usage,tmpfs_runuser1009Size,tmpfs_runuser1009Avail,tmpfs_runuser1009Mounted
```

**支持的格式：**
- `field1,field2,field3`
- `field1, field2, field3` （带空格）
- `field1,field2,field3,field4,field5` （任意数量）

### 3️⃣ 查看所有字段
查看索引中的所有字段，支持搜索和批量选择：

```
请选择 (0-3): 3

📋 索引 psis-collector-harddisk-index 中的所有字段 (共 79 个):
  1. Date                     (类型: date)
  2. IP                       (类型: text)
  ...
 79. tmpfs_tmpUsed            (类型: text)

🔍 可以按关键词搜索字段:
输入搜索关键词（回车跳过）: runuser1009

包含 'runuser1009' 的字段 (共 5 个):
  1. tmpfs_runuser1009Used    (类型: float)
  2. tmpfs_runuser1009Usage   (类型: float)
  3. tmpfs_runuser1009Size    (类型: text)
  4. tmpfs_runuser1009Avail   (类型: text)
  5. tmpfs_runuser1009Mounted (类型: text)

是否删除这 5 个匹配的字段？(y/N): y
```

### 4️⃣ 使用配置文件预设
如果配置文件中有预设字段，可以直接使用：

```
配置文件中的预设字段:
  1. tmpfs_runuser500Used
  2. tmpfs_runuser500Usage
  ...

是否使用配置文件中的预设字段？(y/N): y
```

## 📋 操作流程示例

### 示例1：删除 tmpfs_runuser1009 系列字段

```bash
python core/delete_fields_config.py
```

1. **选择输入方式**：
   ```
   请选择 (0-3): 2
   ```

2. **输入字段列表**：
   ```
   字段列表: tmpfs_runuser1009Used,tmpfs_runuser1009Usage,tmpfs_runuser1009Size,tmpfs_runuser1009Avail,tmpfs_runuser1009Mounted
   ```

3. **确认字段**：
   ```
   📋 确认要删除的字段 (共 5 个):
     1. tmpfs_runuser1009Used
     2. tmpfs_runuser1009Usage
     3. tmpfs_runuser1009Size
     4. tmpfs_runuser1009Avail
     5. tmpfs_runuser1009Mounted
   
   是否确认删除以上 5 个字段？(y/N): y
   ```

4. **自动执行删除**：
   - ✅ 检查索引状态
   - ✅ 分析字段使用情况
   - ✅ 创建新映射
   - ✅ 创建临时索引
   - ✅ 重新索引数据
   - ✅ 验证新索引
   - ✅ 交换索引（保持原索引名）
   - ✅ 最终验证

### 示例2：使用搜索功能批量删除

```bash
python core/delete_fields_config.py
```

1. **查看所有字段**：
   ```
   请选择 (0-3): 3
   ```

2. **搜索相关字段**：
   ```
   输入搜索关键词（回车跳过）: mmcblk0p3
   
   包含 'mmcblk0p3' 的字段 (共 5 个):
     1. mmcblk0p3_Avail
     2. mmcblk0p3_Mounted
     3. mmcblk0p3_Size
     4. mmcblk0p3_Usage
     5. mmcblk0p3_Used
   
   是否删除这 5 个匹配的字段？(y/N): y
   ```

## ⚠️ 重要说明

### 索引名称保持不变
- ✅ **修复后**：删除字段操作不会改变原索引名称
- ✅ 操作完成后，索引名仍然是 `psis-collector-harddisk-index`
- ✅ 不会创建别名，直接使用原索引名

### 安全保障
1. **多重验证**：
   - 字段存在性验证
   - 文档数量验证
   - 样本数据验证

2. **用户确认**：
   - 删除前需要确认字段列表
   - 交换索引前需要确认

3. **自动回滚**：
   - 失败时自动清理临时索引
   - 保留原索引不变

## 🔍 验证结果

删除完成后，可以使用验证脚本检查结果：

```bash
# 验证字段删除结果
python verify/verify_deleted_fields.py

# 或通过主菜单
python main.py
# 选择 5. ✅ 验证操作
# 选择 1. 验证字段删除结果
```

## 🛠️ 故障排除

### 常见问题

#### Q: 提示字段不存在？
A: 检查字段名拼写，可以使用选项3查看所有字段。

#### Q: 操作失败了怎么办？
A: 脚本有自动清理机制，原索引保持不变，可以重新运行。

#### Q: 如何撤销删除操作？
A: 字段删除是不可逆的，请在操作前确认字段列表。

#### Q: 可以删除重要字段吗？
A: 脚本会保留重要字段如 `disk_lsof_tmp_delete`，但请谨慎操作。

## 📊 性能说明

- **处理速度**：约 2-5 分钟（取决于数据量）
- **内存使用**：临时需要双倍存储空间
- **网络影响**：重新索引期间会有网络流量

## 🎯 最佳实践

1. **操作前准备**：
   - 选择业务低峰期操作
   - 确保ES集群资源充足
   - 备份重要数据

2. **字段选择**：
   - 使用搜索功能快速定位相关字段
   - 一次性删除同类字段提高效率
   - 避免删除业务关键字段

3. **操作后验证**：
   - 运行验证脚本确认结果
   - 检查应用程序是否正常
   - 监控索引性能变化

---

💡 **提示**: 新的交互式界面让字段删除变得更加简单和安全！
