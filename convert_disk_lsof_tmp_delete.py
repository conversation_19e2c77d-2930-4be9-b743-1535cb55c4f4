#!/usr/bin/env python3
"""
转换 disk_lsof_tmp_delete 字段从 text 到 integer
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


def convert_disk_lsof_tmp_delete():
    """转换 disk_lsof_tmp_delete 字段"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    FIELD_NAME = "disk_lsof_tmp_delete"
    TEMP_INDEX = f"{INDEX_NAME}_convert_{FIELD_NAME}_{int(time.time())}"
    
    print(f"开始转换字段 {FIELD_NAME} 从 text 到 integer...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 获取当前索引的实际名称（可能是别名）
        aliases = es.indices.get_alias(name=INDEX_NAME)
        if aliases:
            actual_index = list(aliases.keys())[0]
            print(f"✓ 索引 {INDEX_NAME} 是别名，指向: {actual_index}")
        else:
            actual_index = INDEX_NAME
            print(f"✓ 索引 {INDEX_NAME} 是直接索引")
        
        # 2. 获取原索引映射
        print("获取原索引映射...")
        original_mapping = es.indices.get_mapping(index=INDEX_NAME)
        mapping = original_mapping[actual_index]['mappings']
        
        # 3. 修改字段类型
        print(f"修改字段 {FIELD_NAME} 类型为 integer...")
        if 'properties' not in mapping:
            mapping['properties'] = {}
        
        # 保存原字段配置用于对比
        original_field_config = mapping['properties'].get(FIELD_NAME, {})
        print(f"原字段配置: {original_field_config}")
        
        # 修改字段类型
        mapping['properties'][FIELD_NAME] = {
            "type": "integer",
            "null_value": 0
        }
        
        print(f"新字段配置: {mapping['properties'][FIELD_NAME]}")
        
        # 4. 获取原索引设置
        settings = es.indices.get_settings(index=INDEX_NAME)
        index_settings = settings[actual_index]['settings']['index']
        
        # 清理设置
        clean_settings = {k: v for k, v in index_settings.items() 
                         if k not in ['creation_date', 'uuid', 'version', 'provided_name']}
        
        # 5. 创建临时索引
        print(f"创建临时索引 {TEMP_INDEX}...")
        es.indices.create(
            index=TEMP_INDEX,
            body={
                "settings": clean_settings,
                "mappings": mapping
            }
        )
        print("✓ 临时索引创建成功")
        
        # 6. 重新索引数据
        print("重新索引数据...")
        reindex_body = {
            "source": {"index": INDEX_NAME},
            "dest": {"index": TEMP_INDEX},
            "script": {
                "source": f"""
                // 处理 {FIELD_NAME} 字段
                if (ctx._source.{FIELD_NAME} != null) {{
                    String value = ctx._source.{FIELD_NAME}.toString().trim();
                    if (value.isEmpty()) {{
                        ctx._source.{FIELD_NAME} = 0;
                    }} else {{
                        try {{
                            ctx._source.{FIELD_NAME} = Integer.parseInt(value);
                        }} catch (NumberFormatException e) {{
                            ctx._source.{FIELD_NAME} = 0;
                        }}
                    }}
                }} else {{
                    ctx._source.{FIELD_NAME} = 0;
                }}
                """,
                "lang": "painless"
            }
        }
        
        result = es.reindex(body=reindex_body, wait_for_completion=True, timeout="30m")
        print(f"✓ 重新索引完成，处理 {result.get('total', 0)} 条记录")
        
        if result.get('failures'):
            print(f"⚠ 重新索引过程中有失败记录: {result['failures']}")
        
        # 7. 验证新索引
        print("验证新索引...")
        new_mapping = es.indices.get_mapping(index=TEMP_INDEX)
        new_field_type = new_mapping[TEMP_INDEX]['mappings']['properties'][FIELD_NAME]['type']
        print(f"✓ 新索引中字段 {FIELD_NAME} 类型: {new_field_type}")
        
        # 检查样本数据
        search_body = {
            "size": 5,
            "_source": [FIELD_NAME],
            "query": {"match_all": {}}
        }
        
        response = es.search(index=TEMP_INDEX, body=search_body)
        print("样本数据验证:")
        for i, hit in enumerate(response['hits']['hits'], 1):
            source = hit['_source']
            field_value = source.get(FIELD_NAME, "未设置")
            value_type = type(field_value).__name__
            print(f"  {i}. {FIELD_NAME}: {field_value} (类型: {value_type})")
        
        # 8. 确认交换索引
        print("\n" + "=" * 80)
        user_input = input(f"是否要将临时索引替换原索引？这将删除当前的 {INDEX_NAME} (y/N): ")
        
        if user_input.lower() != 'y':
            print(f"操作已取消，临时索引保留为: {TEMP_INDEX}")
            print("您可以稍后手动验证并交换索引")
            return True
        
        # 9. 交换索引
        print("交换索引...")
        
        # 删除原别名
        if aliases:
            es.indices.delete_alias(index=actual_index, name=INDEX_NAME)
            print(f"✓ 已删除别名 {INDEX_NAME}")
            
            # 删除原索引
            es.indices.delete(index=actual_index)
            print(f"✓ 已删除原索引 {actual_index}")
        else:
            es.indices.delete(index=INDEX_NAME)
            print(f"✓ 已删除原索引 {INDEX_NAME}")
        
        # 创建新别名
        es.indices.put_alias(index=TEMP_INDEX, name=INDEX_NAME)
        print(f"✓ 已将临时索引 {TEMP_INDEX} 设置别名为 {INDEX_NAME}")
        
        # 10. 最终验证
        print("最终验证...")
        final_mapping = es.indices.get_mapping(index=INDEX_NAME)
        final_actual_index = list(final_mapping.keys())[0]
        final_field_type = final_mapping[final_actual_index]['mappings']['properties'][FIELD_NAME]['type']
        
        print(f"✓ 最终验证: 字段 {FIELD_NAME} 类型为 {final_field_type}")
        
        print("=" * 80)
        print("✅ 字段转换完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        
        # 清理临时索引
        try:
            if es.indices.exists(index=TEMP_INDEX):
                es.indices.delete(index=TEMP_INDEX)
                print(f"已清理临时索引 {TEMP_INDEX}")
        except:
            pass
        
        return False


if __name__ == "__main__":
    convert_disk_lsof_tmp_delete()
