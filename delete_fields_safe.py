#!/usr/bin/env python3
"""
安全删除Elasticsearch索引中的指定字段列表
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


def delete_fields_safely():
    """安全删除索引中的指定字段"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    
    # 要删除的字段列表
    FIELDS_TO_DELETE = [
        "tmpfs_runuser500Used",
        "tmpfs_runuser500Usage", 
        "tmpfs_runuser500Size",
        "tmpfs_runuser500Avail",
        "tmpfs_runuser500Mounted"
    ]
    
    TEMP_INDEX = f"{INDEX_NAME}_delete_fields_safe_{int(time.time())}"
    
    print(f"安全删除字段操作:")
    print(f"  索引: {INDEX_NAME}")
    print(f"  要删除的字段: {', '.join(FIELDS_TO_DELETE)}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查索引状态
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return False
        
        print(f"✓ 索引 {INDEX_NAME} 存在")
        
        # 检查是否是别名
        aliases = None
        try:
            aliases = es.indices.get_alias(name=INDEX_NAME)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"✓ {INDEX_NAME} 是别名，指向: {actual_index}")
            else:
                actual_index = INDEX_NAME
                print(f"✓ {INDEX_NAME} 是直接索引")
        except:
            actual_index = INDEX_NAME
            aliases = None
        
        # 2. 获取当前索引映射
        print("获取当前索引映射...")
        mapping_response = es.indices.get_mapping(index=INDEX_NAME)
        current_mapping = mapping_response[actual_index]['mappings']
        
        # 3. 检查要删除的字段
        properties = current_mapping.get('properties', {})
        existing_fields = []
        missing_fields = []
        
        for field in FIELDS_TO_DELETE:
            if field in properties:
                existing_fields.append(field)
                field_type = properties[field].get('type', '未知')
                print(f"✓ 找到字段: {field} (类型: {field_type})")
            else:
                missing_fields.append(field)
                print(f"⚠ 字段不存在: {field}")
        
        if not existing_fields:
            print("❌ 没有找到任何要删除的字段")
            return False
        
        print(f"\n将删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
        if missing_fields:
            print(f"跳过 {len(missing_fields)} 个不存在的字段: {', '.join(missing_fields)}")
        
        # 4. 分析字段使用情况
        print("\n分析字段使用情况...")
        for field in existing_fields:
            try:
                # 检查有多少文档包含这个字段
                exists_query = {
                    "size": 0,
                    "query": {
                        "exists": {
                            "field": field
                        }
                    }
                }
                
                result = es.search(index=INDEX_NAME, body=exists_query)
                doc_count = result['hits']['total']['value'] if isinstance(result['hits']['total'], dict) else result['hits']['total']
                print(f"  {field}: {doc_count} 个文档包含此字段")
                
            except Exception as e:
                print(f"  {field}: 无法分析 ({e})")
        
        # 5. 创建新的映射
        print("\n创建新的映射配置...")
        new_mapping = {
            "properties": {}
        }
        
        deleted_count = 0
        kept_count = 0
        
        for field_name, field_config in properties.items():
            if field_name in existing_fields:
                print(f"  - 删除字段: {field_name}")
                deleted_count += 1
            else:
                new_mapping['properties'][field_name] = field_config
                kept_count += 1
        
        print(f"✓ 新映射包含 {kept_count} 个字段 (删除了 {deleted_count} 个字段)")
        
        # 6. 获取索引设置
        settings_response = es.indices.get_settings(index=INDEX_NAME)
        current_settings = settings_response[actual_index]['settings']['index']
        
        # 清理设置
        clean_settings = {}
        for key, value in current_settings.items():
            if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                clean_settings[key] = value
        
        # 7. 创建临时索引
        print(f"创建临时索引 {TEMP_INDEX}...")
        
        create_body = {
            "settings": clean_settings,
            "mappings": new_mapping
        }
        
        es.indices.create(index=TEMP_INDEX, body=create_body)
        print("✓ 临时索引创建成功")
        
        # 8. 重新索引数据（使用更安全的方式）
        print("开始数据迁移...")
        
        reindex_body = {
            "source": {"index": INDEX_NAME},
            "dest": {"index": TEMP_INDEX},
            "script": {
                "source": f"""
                // 删除指定字段，但保留文档
                {chr(10).join([f'if (ctx._source.containsKey("{field}")) {{ ctx._source.remove("{field}"); }}' for field in existing_fields])}
                """,
                "lang": "painless"
            }
        }
        
        # 启动异步重新索引
        result = es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
        task_id = result['task']
        print(f"✓ 数据迁移任务已启动，任务ID: {task_id}")
        
        # 监控重新索引进度
        print("监控数据迁移进度...")
        while True:
            try:
                task_status = es.tasks.get(task_id=task_id)
                
                if task_status['completed']:
                    print("✓ 数据迁移任务完成")
                    response = task_status.get('response', {})
                    print(f"  处理文档数: {response.get('total', 0)}")
                    print(f"  创建文档数: {response.get('created', 0)}")
                    if response.get('failures'):
                        print(f"  失败记录: {len(response['failures'])}")
                        # 显示前几个失败记录
                        for i, failure in enumerate(response['failures'][:3]):
                            print(f"    失败 {i+1}: {failure}")
                    break
                else:
                    task_info = task_status['task']
                    status = task_info.get('status', {})
                    total = status.get('total', 0)
                    created = status.get('created', 0)
                    if total > 0:
                        progress = (created / total) * 100
                        print(f"  进度: {created}/{total} ({progress:.1f}%)")
                    else:
                        print(f"  已处理: {created} 条记录")
                    
                    time.sleep(5)
                    
            except Exception as task_error:
                print(f"⚠ 获取任务状态失败: {task_error}")
                time.sleep(5)
        
        # 9. 验证新索引
        print("验证新索引...")
        
        # 检查文档数量
        old_count = es.count(index=INDEX_NAME)['count']
        new_count = es.count(index=TEMP_INDEX)['count']
        
        print(f"  原索引文档数: {old_count}")
        print(f"  新索引文档数: {new_count}")
        
        if old_count == new_count:
            print("✅ 文档数量验证通过")
        else:
            print(f"⚠ 文档数量不匹配，差异: {old_count - new_count}")
            
            # 继续验证，但给出警告
            user_continue = input("文档数量不匹配，是否继续验证其他方面？(y/N): ")
            if user_continue.lower() != 'y':
                print("操作已取消")
                return False
        
        # 检查字段是否已删除
        new_mapping_check = es.indices.get_mapping(index=TEMP_INDEX)
        new_properties = new_mapping_check[TEMP_INDEX]['mappings']['properties']
        
        print("字段删除验证:")
        all_deleted = True
        for field in existing_fields:
            if field in new_properties:
                print(f"  ❌ {field} 仍然存在（删除失败）")
                all_deleted = False
            else:
                print(f"  ✅ {field} 已成功删除")
        
        if not all_deleted:
            print("❌ 部分字段删除失败")
            return False
        
        # 检查样本数据
        print("\n样本数据验证:")
        search_body = {
            "size": 3,
            "_source": existing_fields + ["disk_lsof_tmp_delete"],
            "query": {"match_all": {}}
        }
        
        try:
            new_response = es.search(index=TEMP_INDEX, body=search_body)
            
            for i, hit in enumerate(new_response['hits']['hits'], 1):
                print(f"  {i}. 文档ID: {hit['_id']}")
                source = hit['_source']
                
                # 检查删除的字段是否还存在
                deleted_fields_found = []
                for field in existing_fields:
                    if field in source:
                        deleted_fields_found.append(field)
                
                if deleted_fields_found:
                    print(f"     ❌ 仍包含已删除字段: {', '.join(deleted_fields_found)}")
                else:
                    print(f"     ✅ 已删除字段不存在")
                
                # 检查保留的字段
                if 'disk_lsof_tmp_delete' in source:
                    print(f"     ✅ 保留字段 disk_lsof_tmp_delete: {source['disk_lsof_tmp_delete']}")
                
        except Exception as search_error:
            print(f"⚠ 样本数据验证失败: {search_error}")
        
        # 10. 确认交换索引
        print("\n" + "=" * 80)
        print("验证摘要:")
        print(f"  - 成功删除字段: {len(existing_fields)}")
        print(f"  - 保留字段数: {len(new_properties)}")
        print(f"  - 文档数变化: {old_count} -> {new_count}")
        
        user_input = input("\n是否用新索引替换原索引？(y/N): ")
        
        if user_input.lower() != 'y':
            print(f"操作已取消，临时索引保留为: {TEMP_INDEX}")
            print("您可以稍后手动验证并交换索引")
            return True
        
        # 11. 交换索引
        print("交换索引...")
        
        # 如果原来是别名，先删除别名
        if aliases:
            es.indices.delete_alias(index=actual_index, name=INDEX_NAME)
            print(f"✓ 已删除别名 {INDEX_NAME}")
            
            # 删除原索引
            es.indices.delete(index=actual_index)
            print(f"✓ 已删除原索引 {actual_index}")
        else:
            # 删除原索引
            es.indices.delete(index=INDEX_NAME)
            print(f"✓ 已删除原索引 {INDEX_NAME}")
        
        # 创建新别名
        es.indices.put_alias(index=TEMP_INDEX, name=INDEX_NAME)
        print(f"✓ 已将临时索引 {TEMP_INDEX} 设置别名为 {INDEX_NAME}")
        
        # 12. 最终验证
        print("最终验证...")
        
        final_mapping = es.indices.get_mapping(index=INDEX_NAME)
        final_actual_index = list(final_mapping.keys())[0]
        final_properties = final_mapping[final_actual_index]['mappings']['properties']
        
        print("最终字段状态:")
        for field in existing_fields:
            if field in final_properties:
                print(f"  ❌ {field} 仍然存在")
            else:
                print(f"  ✅ {field} 已成功删除")
        
        # 检查重要字段
        if 'disk_lsof_tmp_delete' in final_properties:
            field_type = final_properties['disk_lsof_tmp_delete'].get('type', '未知')
            print(f"  ✅ disk_lsof_tmp_delete 保留，类型: {field_type}")
        
        final_count = es.count(index=INDEX_NAME)['count']
        print(f"✅ 最终文档数: {final_count}")
        
        print("=" * 80)
        print("✅ 字段删除操作完成！")
        print(f"已成功删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        
        # 清理临时索引
        try:
            if es.indices.exists(index=TEMP_INDEX):
                es.indices.delete(index=TEMP_INDEX)
                print(f"已清理临时索引 {TEMP_INDEX}")
        except:
            pass
        
        return False


if __name__ == "__main__":
    delete_fields_safely()
