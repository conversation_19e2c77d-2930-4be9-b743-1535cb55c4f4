# 历史版本和废弃脚本

这个目录包含旧版本、实验性脚本和已被替代的脚本。

## 文件说明

### delete_fields_complete.py
- **状态**: 已被 `core/delete_fields_config.py` 替代
- **功能**: 完整的字段删除功能
- **说明**: 面向对象设计，功能完整但配置复杂

### delete_fields.py / delete_fields_safe.py
- **状态**: 早期版本，已废弃
- **功能**: 字段删除的早期实现
- **说明**: 功能不完整，建议使用新版本

### simple_es_converter.py
- **状态**: 简化版本
- **功能**: 简单的字段类型转换
- **说明**: 功能有限，建议使用完整版

### 其他脚本
- **convert_disk_lsof_tmp_delete.py**: 特定字段的转换脚本
- **rename_and_delete_fields.py**: 组合操作脚本
- **rename_index.py**: 索引重命名早期版本

## 注意事项

⚠️ 这些脚本仅供参考，不建议在生产环境使用。
✅ 请使用 `core/` 目录中的最新版本。
