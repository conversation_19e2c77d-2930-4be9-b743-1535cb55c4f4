# 📖 Elasticsearch字段操作 - 快速使用指南

## 🎯 最常用操作

### 1️⃣ 删除字段（90%的使用场景）

```bash
# 第一步：编辑配置
nano core/delete_fields_config.py

# 第二步：修改字段列表
FIELDS_TO_DELETE = [
    "要删除的字段1",
    "要删除的字段2",
    "要删除的字段3",
]

# 第三步：执行删除
python core/delete_fields_config.py

# 第四步：验证结果
python verify/verify_deleted_fields.py
```

### 2️⃣ 检查字段信息

```bash
# 查看所有字段信息
python utils/check_fields.py

# 检查索引状态
python utils/check_index_status.py
```

## 📚 详细操作指南

### 🗑️ 字段删除操作

#### 准备工作
1. **确认要删除的字段**
   ```bash
   python utils/check_fields.py
   ```

2. **备份重要数据**（可选但推荐）

#### 执行删除
1. **编辑配置文件**
   ```bash
   # 打开配置文件
   nano core/delete_fields_config.py
   
   # 或使用其他编辑器
   code core/delete_fields_config.py
   ```

2. **修改字段列表**
   ```python
   # 在配置文件中找到这个列表
   FIELDS_TO_DELETE = [
       # 示例：删除tmpfs相关字段
       "tmpfs_runuser1009Used",
       "tmpfs_runuser1009Usage",
       "tmpfs_runuser1009Size",
       "tmpfs_runuser1009Avail",
       "tmpfs_runuser1009Mounted",
       
       # 添加您要删除的字段
       # "your_field_name_1",
       # "your_field_name_2",
   ]
   ```

3. **运行删除脚本**
   ```bash
   python core/delete_fields_config.py
   ```

4. **按提示操作**
   - 脚本会显示找到的字段
   - 显示字段使用情况
   - 要求确认删除操作
   - 监控删除进度

5. **验证删除结果**
   ```bash
   python verify/verify_deleted_fields.py
   ```

### 🔄 字段类型转换

#### 使用场景
- 将text字段转换为integer
- 设置字段默认值
- 修改字段属性

#### 操作步骤
1. **运行转换脚本**
   ```bash
   python core/es_field_type_converter.py
   ```

2. **按提示配置**
   - 输入要转换的字段名
   - 选择目标类型
   - 设置默认值

3. **验证转换结果**
   ```bash
   python verify/verify_conversion.py
   ```

### 🔍 状态检查和诊断

#### 检查字段信息
```bash
# 查看所有字段的类型和使用情况
python utils/check_fields.py
```

#### 检查索引状态
```bash
# 检查索引健康状态，修复别名问题
python utils/check_index_status.py
```

#### 验证操作结果
```bash
# 验证字段删除
python verify/verify_deleted_fields.py

# 验证字段转换
python verify/verify_conversion.py

# 验证特定字段删除
python verify/verify_new_deleted_fields.py
```

## ⚠️ 注意事项

### 安全提醒
1. **重要操作前备份数据**
2. **先在测试环境验证**
3. **仔细检查字段名称**
4. **确认操作前仔细阅读提示**

### 常见问题

#### Q: 删除字段后文档数量不匹配？
A: 这通常是正常的，可能是因为：
- 删除了空文档
- 清理了无效数据
- 脚本会询问是否继续，选择 'y' 继续

#### Q: 索引名称变成了临时名称？
A: 使用索引重命名工具：
```bash
python utils/rename_index_final.py
```

#### Q: 操作失败了怎么办？
A: 脚本有自动回滚机制：
- 失败时会自动清理临时索引
- 原索引保持不变
- 可以重新运行脚本

#### Q: 如何撤销删除操作？
A: 字段删除是不可逆的，需要：
- 从备份恢复数据
- 或重新导入原始数据

## 🛠️ 高级用法

### 批量操作
```python
# 可以一次删除多个相关字段
FIELDS_TO_DELETE = [
    # 删除所有tmpfs_runuser500相关字段
    "tmpfs_runuser500Used",
    "tmpfs_runuser500Usage", 
    "tmpfs_runuser500Size",
    "tmpfs_runuser500Avail",
    "tmpfs_runuser500Mounted",
    
    # 删除所有tmpfs_runuser1009相关字段
    "tmpfs_runuser1009Used",
    "tmpfs_runuser1009Usage",
    "tmpfs_runuser1009Size", 
    "tmpfs_runuser1009Avail",
    "tmpfs_runuser1009Mounted",
]
```

### 自定义配置
```python
# 修改ES连接配置
ES_CONFIG = {
    "host": "http://your-es-host:9200",
    "index_name": "your-index-name",
    "timeout": 30,
}

# 修改操作配置
OPERATION_CONFIG = {
    "confirm_before_swap": True,  # 是否需要确认
    "auto_cleanup": True,         # 是否自动清理
    "show_progress": True,        # 是否显示进度
}
```

## 📞 获取帮助

1. **查看脚本帮助**
   ```bash
   python core/delete_fields_config.py --help
   ```

2. **查看目录说明**
   ```bash
   cat core/README.md
   cat utils/README.md
   cat verify/README.md
   ```

3. **检查日志输出**
   - 所有脚本都有详细的日志输出
   - 仔细阅读每个步骤的提示信息

## 🎉 成功案例

### 已完成的操作历史
1. ✅ 字段类型转换：`disk_lsof_tmp_deletey` (text → integer)
2. ✅ 字段重命名：`disk_lsof_tmp_deletey` → `disk_lsof_tmp_delete`
3. ✅ 批量删除：tmpfs_runuser500* 系列字段（5个）
4. ✅ 批量删除：tmpfs_runuser1009* 系列字段（5个）

### 当前索引状态
- **索引名称**: `psis-collector-harddisk-index`
- **文档总数**: 127,793 条
- **字段总数**: 79 个
- **重要字段**: `disk_lsof_tmp_delete` (integer类型)

---

💡 **提示**: 大部分情况下，您只需要使用 `core/delete_fields_config.py` 就能完成字段删除任务！
