#!/usr/bin/env python3
"""
交互式检查索引中字段的详细信息
"""

from elasticsearch import Elasticsearch
import json


def get_fields_from_user():
    """从用户输入获取要检查的字段列表"""
    print("📝 请输入要检查的字段")
    print("=" * 60)
    print("支持以下输入方式:")
    print("1. 逐个输入字段名（每行一个，输入空行结束）")
    print("2. 一次性输入多个字段（用逗号分隔）")
    print("3. 查看索引中的所有字段")
    print("4. 按关键词搜索字段")
    print("-" * 60)

    print("\n请选择操作:")
    print("1. 逐个输入字段名")
    print("2. 一次性输入多个字段（用逗号分隔）")
    print("3. 查看索引中的所有字段")
    print("4. 按关键词搜索字段")
    print("0. 退出")

    while True:
        choice = input("请选择 (0-4): ").strip()
        if choice in ['0', '1', '2', '3', '4']:
            break
        print("❌ 请输入 0-4")

    if choice == '0':
        print("👋 退出")
        return None

    if choice == '3':
        # 查看所有字段
        show_all_fields()
        return get_fields_from_user()  # 递归调用，让用户重新选择

    if choice == '4':
        # 搜索字段
        search_result = search_fields()
        if search_result:
            return search_result
        return get_fields_from_user()  # 递归调用，让用户重新选择

    fields = []

    if choice == '1':
        # 逐个输入
        print("\n请逐个输入字段名（输入空行结束）:")
        while True:
            field = input("字段名: ").strip()
            if not field:
                break
            if field in fields:
                print(f"⚠ 字段 {field} 已存在，跳过")
                continue
            fields.append(field)
            print(f"✓ 已添加字段: {field}")

    else:  # choice == '2'
        # 一次性输入
        print("\n请输入要检查的字段（用逗号分隔）:")
        print("示例: field1,field2,field3")
        print("或者: field1, field2, field3")

        while True:
            input_text = input("字段列表: ").strip()
            if input_text:
                # 分割字段并清理空白
                raw_fields = [f.strip() for f in input_text.split(',')]
                fields = [f for f in raw_fields if f]  # 过滤空字符串

                if fields:
                    break
                else:
                    print("❌ 没有有效的字段名，请重新输入")
            else:
                print("❌ 输入不能为空，请重新输入")

    if not fields:
        print("❌ 没有输入任何字段")
        return None

    # 确认字段列表
    print(f"\n📋 确认要检查的字段 (共 {len(fields)} 个):")
    for i, field in enumerate(fields, 1):
        print(f"  {i}. {field}")

    confirm = input(f"\n是否确认检查以上 {len(fields)} 个字段？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消")
        return None

    return fields


def show_all_fields():
    """显示索引中的所有字段"""
    try:
        print("\n🔍 正在获取索引字段信息...")

        # ES配置
        ES_HOST = "http://***************:9200"
        INDEX_NAME = "psis-collector-harddisk-index"

        # 连接ES
        es = Elasticsearch([ES_HOST], request_timeout=30)
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return

        # 检查索引是否存在
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return

        # 获取映射
        mapping_response = es.indices.get_mapping(index=INDEX_NAME)

        # 处理别名情况
        actual_index = list(mapping_response.keys())[0]
        properties = mapping_response[actual_index]['mappings'].get('properties', {})

        if not properties:
            print("❌ 索引中没有字段")
            return

        print(f"\n📋 索引 {INDEX_NAME} 中的所有字段 (共 {len(properties)} 个):")
        print("-" * 80)

        # 按字段名排序
        sorted_fields = sorted(properties.items())

        for i, (field_name, field_config) in enumerate(sorted_fields, 1):
            field_type = field_config.get('type', '未知')
            print(f"{i:3d}. {field_name:<40} (类型: {field_type})")

        print("-" * 80)
        print(f"总计: {len(properties)} 个字段")

        input("\n按回车键继续...")

    except Exception as e:
        print(f"❌ 获取字段信息失败: {e}")


def search_fields():
    """搜索字段"""
    try:
        # ES配置
        ES_HOST = "http://***************:9200"
        INDEX_NAME = "psis-collector-harddisk-index"

        # 连接ES
        es = Elasticsearch([ES_HOST], request_timeout=30)
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return None

        # 获取映射
        mapping_response = es.indices.get_mapping(index=INDEX_NAME)
        actual_index = list(mapping_response.keys())[0]
        properties = mapping_response[actual_index]['mappings'].get('properties', {})

        print("\n🔍 按关键词搜索字段:")
        search_term = input("输入搜索关键词: ").strip()

        if not search_term:
            print("❌ 搜索关键词不能为空")
            return None

        matching_fields = [name for name in properties.keys() if search_term.lower() in name.lower()]

        if matching_fields:
            print(f"\n包含 '{search_term}' 的字段 (共 {len(matching_fields)} 个):")
            for i, field_name in enumerate(matching_fields, 1):
                field_type = properties[field_name].get('type', '未知')
                print(f"{i:3d}. {field_name:<40} (类型: {field_type})")

            # 询问是否使用搜索结果
            use_search = input(f"\n是否检查这 {len(matching_fields)} 个匹配的字段？(y/N): ").strip().lower()
            if use_search == 'y':
                return matching_fields
        else:
            print(f"❌ 没有找到包含 '{search_term}' 的字段")

        return None

    except Exception as e:
        print(f"❌ 搜索字段失败: {e}")
        return None


def check_fields_interactive():
    """交互式检查索引中的字段"""

    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"

    # 获取要检查的字段列表
    fields_to_check = get_fields_from_user()
    if not fields_to_check:
        return False

    print(f"\n🔍 检查索引 {INDEX_NAME} 中的字段信息...")
    print("=" * 80)

    try:
        # 连接ES
        es = Elasticsearch([ES_HOST], request_timeout=30)

        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False

        print("✓ 成功连接到Elasticsearch")

        # 获取映射信息
        mapping_info = es.indices.get_mapping(index=INDEX_NAME)
        actual_index = list(mapping_info.keys())[0]
        mappings = mapping_info[actual_index]['mappings']

        print(f"✓ 实际索引名: {actual_index}")

        # 检查指定字段
        if 'properties' in mappings:
            properties = mappings['properties']

            print(f"\n检查 {len(fields_to_check)} 个指定字段:")
            print("-" * 60)
            
            for field_name, field_config in related_fields:
                print(f"字段名: {field_name}")
                print(f"  类型: {field_config.get('type', '未知')}")
                if 'null_value' in field_config:
                    print(f"  默认值: {field_config['null_value']}")
                if 'analyzer' in field_config:
                    print(f"  分析器: {field_config['analyzer']}")
                print()
        
        # 获取样本数据检查这些字段的值
        print("获取样本数据检查字段值...")
        print("-" * 60)
        
        for field_name, _ in related_fields:
            print(f"\n检查字段: {field_name}")
            
            try:
                # 搜索包含该字段的文档
                search_body = {
                    "size": 5,
                    "_source": [field_name],
                    "query": {
                        "exists": {
                            "field": field_name
                        }
                    }
                }
                
                response = es.search(index=INDEX_NAME, body=search_body)
                
                if response['hits']['hits']:
                    print(f"  样本数据:")
                    for i, hit in enumerate(response['hits']['hits'], 1):
                        source = hit['_source']
                        field_value = source.get(field_name, "字段不存在")
                        value_type = type(field_value).__name__
                        print(f"    {i}. {field_name}: {field_value} (类型: {value_type})")
                else:
                    print(f"  ⚠ 没有找到包含字段 {field_name} 的文档")
                
                # 统计该字段的值分布
                agg_body = {
                    "size": 0,
                    "aggs": {
                        "field_values": {
                            "terms": {
                                "field": field_name,
                                "size": 10
                            }
                        }
                    }
                }
                
                try:
                    agg_response = es.search(index=INDEX_NAME, body=agg_body)
                    if 'aggregations' in agg_response and 'field_values' in agg_response['aggregations']:
                        buckets = agg_response['aggregations']['field_values']['buckets']
                        if buckets:
                            print(f"  值分布:")
                            for bucket in buckets[:5]:  # 只显示前5个
                                print(f"    值: {bucket['key']} -> 文档数: {bucket['doc_count']}")
                        else:
                            print(f"  ⚠ 没有找到该字段的值分布")
                except Exception as agg_error:
                    print(f"  ⚠ 聚合查询失败: {agg_error}")
                    
            except Exception as search_error:
                print(f"  ❌ 搜索失败: {search_error}")
        
        # 检查是否需要转换 disk_lsof_tmp_delete 字段
        print("\n" + "=" * 80)
        print("字段转换建议:")
        print("-" * 60)
        
        for field_name, field_config in related_fields:
            field_type = field_config.get('type', '未知')
            
            if field_name == "disk_lsof_tmp_deletey":
                if field_type == "integer":
                    print(f"✅ {field_name}: 已转换为 integer 类型")
                else:
                    print(f"⚠ {field_name}: 类型为 {field_type}，可能需要转换")
            
            elif field_name == "disk_lsof_tmp_delete":
                if field_type == "text":
                    print(f"🔄 {field_name}: 类型为 text，建议转换为 integer")
                elif field_type == "integer":
                    print(f"✅ {field_name}: 已经是 integer 类型")
                else:
                    print(f"ℹ️ {field_name}: 类型为 {field_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程失败: {e}")
        return False


def convert_additional_field(field_name):
    """转换额外的字段"""
    print(f"\n是否要转换字段 {field_name} 从 text 到 integer？")
    user_input = input("输入 'y' 确认转换，其他任意键取消: ")
    
    if user_input.lower() == 'y':
        print(f"开始转换字段 {field_name}...")
        
        # 导入转换器类
        from es_field_type_converter import ESFieldTypeConverter
        
        # 创建转换器实例，指定要转换的字段
        converter = ESFieldTypeConverter()
        converter.field_name = field_name  # 修改要转换的字段名
        converter.temp_index_name = f"{converter.index_name}_temp_{field_name}_{int(time.time())}"
        
        # 执行转换
        converter.run(confirm_swap=True)
    else:
        print("取消转换")


if __name__ == "__main__":
    import time
    
    if check_fields():
        print("\n" + "=" * 80)
        print("如果需要转换 disk_lsof_tmp_delete 字段，请运行:")
        print("python -c \"from check_fields import convert_additional_field; convert_additional_field('disk_lsof_tmp_delete')\"")
