#!/usr/bin/env python3
"""
将索引名从 psis-collector-harddisk-index_rename_1749453745 改回 psis-collector-harddisk-index
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


def rename_index_back():
    """将索引名改回原名"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    ALIAS_NAME = "psis-collector-harddisk-index"
    CURRENT_INDEX = "psis-collector-harddisk-index_rename_1749453745"
    TARGET_INDEX = "psis-collector-harddisk-index"
    
    print(f"开始索引重命名操作:")
    print(f"  当前索引: {CURRENT_INDEX}")
    print(f"  目标索引: {TARGET_INDEX}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查当前状态
        print("检查当前索引状态...")
        
        # 检查别名是否存在
        try:
            aliases = es.indices.get_alias(name=ALIAS_NAME)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"✓ 别名 {ALIAS_NAME} 存在，指向: {actual_index}")
                
                if actual_index != CURRENT_INDEX:
                    print(f"⚠ 警告: 别名指向的索引 ({actual_index}) 与预期不符 ({CURRENT_INDEX})")
                    CURRENT_INDEX = actual_index  # 使用实际的索引名
            else:
                print(f"⚠ 别名 {ALIAS_NAME} 不存在")
                
        except Exception as alias_error:
            print(f"⚠ 检查别名失败: {alias_error}")
        
        # 检查当前索引是否存在
        if not es.indices.exists(index=CURRENT_INDEX):
            print(f"❌ 当前索引 {CURRENT_INDEX} 不存在")
            return False
        
        print(f"✓ 当前索引 {CURRENT_INDEX} 存在")
        
        # 检查目标索引名是否已被占用（排除别名情况）
        target_exists = es.indices.exists(index=TARGET_INDEX)
        if target_exists:
            # 检查是否是别名
            try:
                target_aliases = es.indices.get_alias(name=TARGET_INDEX)
                if target_aliases:
                    print(f"✓ {TARGET_INDEX} 当前是别名，将被删除并替换为实际索引")
                else:
                    print(f"❌ 目标索引名 {TARGET_INDEX} 已被实际索引占用")
                    return False
            except:
                print(f"❌ 目标索引名 {TARGET_INDEX} 已被占用")
                return False
        else:
            print(f"✓ 目标索引名 {TARGET_INDEX} 可用")
        
        # 2. 获取当前索引的设置和映射
        print("获取索引设置和映射...")
        
        # 获取设置
        settings_response = es.indices.get_settings(index=CURRENT_INDEX)
        current_settings = settings_response[CURRENT_INDEX]['settings']['index']
        
        # 清理设置中不需要的字段
        clean_settings = {}
        for key, value in current_settings.items():
            if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                clean_settings[key] = value
        
        # 获取映射
        mapping_response = es.indices.get_mapping(index=CURRENT_INDEX)
        current_mapping = mapping_response[CURRENT_INDEX]['mappings']
        
        print("✓ 成功获取索引设置和映射")
        
        # 3. 删除现有别名（如果存在）
        if aliases:
            print(f"删除现有别名 {ALIAS_NAME}...")
            es.indices.delete_alias(index=CURRENT_INDEX, name=ALIAS_NAME)
            print(f"✓ 已删除别名 {ALIAS_NAME}")

        # 4. 创建新索引
        print(f"创建新索引 {TARGET_INDEX}...")

        create_body = {
            "settings": clean_settings,
            "mappings": current_mapping
        }

        es.indices.create(index=TARGET_INDEX, body=create_body)
        print(f"✓ 新索引 {TARGET_INDEX} 创建成功")
        
        # 4. 重新索引数据
        print("开始数据迁移...")
        
        reindex_body = {
            "source": {"index": CURRENT_INDEX},
            "dest": {"index": TARGET_INDEX}
        }
        
        # 启动异步重新索引
        result = es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
        task_id = result['task']
        print(f"✓ 数据迁移任务已启动，任务ID: {task_id}")
        
        # 监控重新索引进度
        print("监控数据迁移进度...")
        while True:
            try:
                task_status = es.tasks.get(task_id=task_id)
                
                if task_status['completed']:
                    print("✓ 数据迁移任务完成")
                    response = task_status.get('response', {})
                    print(f"  处理文档数: {response.get('total', 0)}")
                    print(f"  创建文档数: {response.get('created', 0)}")
                    if response.get('failures'):
                        print(f"  失败记录: {len(response['failures'])}")
                    break
                else:
                    task_info = task_status['task']
                    status = task_info.get('status', {})
                    total = status.get('total', 0)
                    created = status.get('created', 0)
                    if total > 0:
                        progress = (created / total) * 100
                        print(f"  进度: {created}/{total} ({progress:.1f}%)")
                    else:
                        print(f"  已处理: {created} 条记录")
                    
                    time.sleep(5)  # 等待5秒后再次检查
                    
            except Exception as task_error:
                print(f"⚠ 获取任务状态失败: {task_error}")
                time.sleep(5)
        
        # 5. 验证新索引
        print("验证新索引...")
        
        # 检查文档数量
        old_count = es.count(index=CURRENT_INDEX)['count']
        new_count = es.count(index=TARGET_INDEX)['count']
        
        print(f"  原索引文档数: {old_count}")
        print(f"  新索引文档数: {new_count}")
        
        if old_count == new_count:
            print("✅ 文档数量验证通过")
        else:
            print("❌ 文档数量不匹配")
            return False
        
        # 检查样本数据
        search_body = {
            "size": 3,
            "_source": ["disk_lsof_tmp_delete"],
            "query": {"match_all": {}}
        }
        
        old_response = es.search(index=CURRENT_INDEX, body=search_body)
        new_response = es.search(index=TARGET_INDEX, body=search_body)
        
        print("  样本数据验证:")
        for i, (old_hit, new_hit) in enumerate(zip(old_response['hits']['hits'], new_response['hits']['hits']), 1):
            old_value = old_hit['_source'].get('disk_lsof_tmp_delete', 'N/A')
            new_value = new_hit['_source'].get('disk_lsof_tmp_delete', 'N/A')
            print(f"    {i}. 原: {old_value}, 新: {new_value}")
        
        # 6. 确认删除旧索引
        print("\n" + "=" * 80)
        user_input = input(f"验证通过！是否删除旧索引和别名？(y/N): ")
        
        if user_input.lower() != 'y':
            print("操作已取消")
            print(f"新索引 {TARGET_INDEX} 已创建，您可以稍后手动清理")
            return True
        
        # 7. 删除别名和旧索引
        print("清理旧索引和别名...")
        
        # 删除别名
        try:
            if aliases:
                es.indices.delete_alias(index=CURRENT_INDEX, name=ALIAS_NAME)
                print(f"✓ 已删除别名 {ALIAS_NAME}")
        except Exception as alias_del_error:
            print(f"⚠ 删除别名失败: {alias_del_error}")
        
        # 删除旧索引
        es.indices.delete(index=CURRENT_INDEX)
        print(f"✓ 已删除旧索引 {CURRENT_INDEX}")
        
        # 8. 最终验证
        print("最终验证...")
        
        if es.indices.exists(index=TARGET_INDEX):
            print(f"✅ 新索引 {TARGET_INDEX} 存在")
            
            # 检查索引状态
            final_count = es.count(index=TARGET_INDEX)['count']
            print(f"✅ 最终文档数: {final_count}")
            
            # 检查字段映射
            final_mapping = es.indices.get_mapping(index=TARGET_INDEX)
            properties = final_mapping[TARGET_INDEX]['mappings']['properties']
            
            if 'disk_lsof_tmp_delete' in properties:
                field_type = properties['disk_lsof_tmp_delete'].get('type', '未知')
                print(f"✅ disk_lsof_tmp_delete 字段存在，类型: {field_type}")
            else:
                print("⚠ disk_lsof_tmp_delete 字段不存在")
        else:
            print(f"❌ 新索引 {TARGET_INDEX} 不存在")
            return False
        
        print("=" * 80)
        print("✅ 索引重命名完成！")
        print(f"索引名已从 {CURRENT_INDEX} 改为 {TARGET_INDEX}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        
        # 清理可能创建的新索引
        try:
            if es.indices.exists(index=TARGET_INDEX):
                es.indices.delete(index=TARGET_INDEX)
                print(f"已清理新索引 {TARGET_INDEX}")
        except:
            pass
        
        return False


if __name__ == "__main__":
    rename_index_back()
