#!/usr/bin/env python3
"""
Elasticsearch字段操作 - 主入口脚本
提供交互式菜单，方便选择不同功能
"""

import os
import sys
import subprocess


def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 Elasticsearch字段操作工具")
    print("   ES服务器: http://100.120.180.251:9200")
    print("   目标索引: psis-collector-harddisk-index")
    print("=" * 60)


def print_menu():
    """打印主菜单"""
    print("\n📋 请选择操作:")
    print("1. 🗑️  删除字段 (最常用)")
    print("2. 🔄 字段类型转换")
    print("3. 🔍 检查字段信息")
    print("4. 📊 检查索引状态")
    print("5. ✅ 验证操作结果")
    print("6. 🛠️  工具和修复")
    print("7. 📖 查看使用指南")
    print("0. 🚪 退出")
    print("-" * 40)


def run_script(script_path, description=""):
    """运行指定脚本"""
    if not os.path.exists(script_path):
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    print(f"\n🚀 运行: {description or script_path}")
    print("-" * 40)
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=False, 
                              text=True)
        
        print("-" * 40)
        if result.returncode == 0:
            print("✅ 脚本执行完成")
        else:
            print(f"⚠️ 脚本执行完成，返回码: {result.returncode}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def delete_fields_menu():
    """字段删除子菜单"""
    print("\n🗑️ 字段删除操作")
    print("1. 配置并删除字段 (推荐)")
    print("2. 查看配置文件")
    print("3. 验证删除结果")
    print("0. 返回主菜单")
    
    choice = input("\n请选择: ").strip()
    
    if choice == "1":
        print("\n📝 请先编辑配置文件，然后运行删除脚本")
        print("配置文件位置: core/delete_fields_config.py")
        print("需要修改 FIELDS_TO_DELETE 列表")
        
        confirm = input("\n是否已经配置好字段列表？(y/N): ").strip().lower()
        if confirm == 'y':
            run_script("core/delete_fields_config.py", "字段删除脚本")
        else:
            print("请先编辑配置文件，然后重新运行此选项")
    
    elif choice == "2":
        config_path = "core/delete_fields_config.py"
        if os.path.exists(config_path):
            print(f"\n📄 配置文件内容预览:")
            print("-" * 40)
            with open(config_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                in_fields_section = False
                for i, line in enumerate(lines):
                    if 'FIELDS_TO_DELETE' in line:
                        in_fields_section = True
                    if in_fields_section:
                        print(f"{i+1:3d}: {line.rstrip()}")
                        if line.strip() == ']':
                            break
            print("-" * 40)
            print(f"完整文件路径: {os.path.abspath(config_path)}")
        else:
            print("❌ 配置文件不存在")
    
    elif choice == "3":
        run_script("verify/verify_deleted_fields.py", "验证字段删除结果")
    
    elif choice == "0":
        return
    
    else:
        print("❌ 无效选择")


def verify_menu():
    """验证操作子菜单"""
    print("\n✅ 验证操作")
    print("1. 验证字段删除结果")
    print("2. 验证字段类型转换")
    print("3. 验证最新删除的字段")
    print("0. 返回主菜单")
    
    choice = input("\n请选择: ").strip()
    
    if choice == "1":
        run_script("verify/verify_deleted_fields.py", "验证字段删除")
    elif choice == "2":
        run_script("verify/verify_conversion.py", "验证字段转换")
    elif choice == "3":
        run_script("verify/verify_new_deleted_fields.py", "验证新删除字段")
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")


def tools_menu():
    """工具和修复子菜单"""
    print("\n🛠️ 工具和修复")
    print("1. 索引重命名")
    print("2. 清理临时索引")
    print("3. 修复索引状态")
    print("0. 返回主菜单")
    
    choice = input("\n请选择: ").strip()
    
    if choice == "1":
        run_script("utils/rename_index_interactive.py", "交互式索引重命名")
    elif choice == "2" or choice == "3":
        run_script("utils/check_index_status.py", "检查和修复索引状态")
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")


def show_guide():
    """显示使用指南"""
    guide_files = [
        ("USAGE_GUIDE.md", "快速使用指南"),
        ("README.md", "项目说明"),
        ("core/README.md", "核心功能说明"),
        ("utils/README.md", "工具功能说明"),
        ("verify/README.md", "验证功能说明")
    ]
    
    print("\n📖 使用指南")
    for i, (file_path, description) in enumerate(guide_files, 1):
        print(f"{i}. {description}")
    print("0. 返回主菜单")
    
    choice = input("\n请选择要查看的指南: ").strip()
    
    try:
        choice_idx = int(choice) - 1
        if 0 <= choice_idx < len(guide_files):
            file_path, description = guide_files[choice_idx]
            if os.path.exists(file_path):
                print(f"\n📄 {description}")
                print("=" * 60)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 只显示前50行，避免输出过长
                    lines = content.split('\n')
                    for line in lines[:50]:
                        print(line)
                    if len(lines) > 50:
                        print(f"\n... (还有 {len(lines) - 50} 行，请直接查看文件)")
                print("=" * 60)
                print(f"完整文件路径: {os.path.abspath(file_path)}")
            else:
                print(f"❌ 文件不存在: {file_path}")
        elif choice == "0":
            return
        else:
            print("❌ 无效选择")
    except ValueError:
        if choice == "0":
            return
        print("❌ 请输入数字")


def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        choice = input("请选择操作 (0-7): ").strip()
        
        if choice == "1":
            delete_fields_menu()
        
        elif choice == "2":
            run_script("core/es_field_type_converter.py", "字段类型转换")
        
        elif choice == "3":
            run_script("utils/check_fields.py", "检查字段信息")
        
        elif choice == "4":
            run_script("utils/check_index_status.py", "检查索引状态")
        
        elif choice == "5":
            verify_menu()
        
        elif choice == "6":
            tools_menu()
        
        elif choice == "7":
            show_guide()
        
        elif choice == "0":
            print("\n👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请输入 0-7")
        
        # 等待用户按键继续
        if choice != "0":
            input("\n按回车键继续...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
