#!/usr/bin/env python3
"""
检查索引中相关字段的详细信息
"""

from elasticsearch import Elasticsearch
import json


def check_fields():
    """检查索引中的相关字段"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    
    print(f"检查索引 {INDEX_NAME} 中的字段信息...")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 获取映射信息
        mapping_info = es.indices.get_mapping(index=INDEX_NAME)
        actual_index = list(mapping_info.keys())[0]
        mappings = mapping_info[actual_index]['mappings']
        
        print(f"✓ 实际索引名: {actual_index}")
        
        # 查找相关字段
        target_fields = ["disk_lsof_tmp_delete", "disk_lsof_tmp_deletey"]
        related_fields = []
        
        if 'properties' in mappings:
            properties = mappings['properties']
            
            # 查找包含 "disk_lsof_tmp_delete" 的所有字段
            for field_name, field_config in properties.items():
                if "disk_lsof_tmp_delete" in field_name:
                    related_fields.append((field_name, field_config))
            
            print(f"\n找到 {len(related_fields)} 个相关字段:")
            print("-" * 60)
            
            for field_name, field_config in related_fields:
                print(f"字段名: {field_name}")
                print(f"  类型: {field_config.get('type', '未知')}")
                if 'null_value' in field_config:
                    print(f"  默认值: {field_config['null_value']}")
                if 'analyzer' in field_config:
                    print(f"  分析器: {field_config['analyzer']}")
                print()
        
        # 获取样本数据检查这些字段的值
        print("获取样本数据检查字段值...")
        print("-" * 60)
        
        for field_name, _ in related_fields:
            print(f"\n检查字段: {field_name}")
            
            try:
                # 搜索包含该字段的文档
                search_body = {
                    "size": 5,
                    "_source": [field_name],
                    "query": {
                        "exists": {
                            "field": field_name
                        }
                    }
                }
                
                response = es.search(index=INDEX_NAME, body=search_body)
                
                if response['hits']['hits']:
                    print(f"  样本数据:")
                    for i, hit in enumerate(response['hits']['hits'], 1):
                        source = hit['_source']
                        field_value = source.get(field_name, "字段不存在")
                        value_type = type(field_value).__name__
                        print(f"    {i}. {field_name}: {field_value} (类型: {value_type})")
                else:
                    print(f"  ⚠ 没有找到包含字段 {field_name} 的文档")
                
                # 统计该字段的值分布
                agg_body = {
                    "size": 0,
                    "aggs": {
                        "field_values": {
                            "terms": {
                                "field": field_name,
                                "size": 10
                            }
                        }
                    }
                }
                
                try:
                    agg_response = es.search(index=INDEX_NAME, body=agg_body)
                    if 'aggregations' in agg_response and 'field_values' in agg_response['aggregations']:
                        buckets = agg_response['aggregations']['field_values']['buckets']
                        if buckets:
                            print(f"  值分布:")
                            for bucket in buckets[:5]:  # 只显示前5个
                                print(f"    值: {bucket['key']} -> 文档数: {bucket['doc_count']}")
                        else:
                            print(f"  ⚠ 没有找到该字段的值分布")
                except Exception as agg_error:
                    print(f"  ⚠ 聚合查询失败: {agg_error}")
                    
            except Exception as search_error:
                print(f"  ❌ 搜索失败: {search_error}")
        
        # 检查是否需要转换 disk_lsof_tmp_delete 字段
        print("\n" + "=" * 80)
        print("字段转换建议:")
        print("-" * 60)
        
        for field_name, field_config in related_fields:
            field_type = field_config.get('type', '未知')
            
            if field_name == "disk_lsof_tmp_deletey":
                if field_type == "integer":
                    print(f"✅ {field_name}: 已转换为 integer 类型")
                else:
                    print(f"⚠ {field_name}: 类型为 {field_type}，可能需要转换")
            
            elif field_name == "disk_lsof_tmp_delete":
                if field_type == "text":
                    print(f"🔄 {field_name}: 类型为 text，建议转换为 integer")
                elif field_type == "integer":
                    print(f"✅ {field_name}: 已经是 integer 类型")
                else:
                    print(f"ℹ️ {field_name}: 类型为 {field_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程失败: {e}")
        return False


def convert_additional_field(field_name):
    """转换额外的字段"""
    print(f"\n是否要转换字段 {field_name} 从 text 到 integer？")
    user_input = input("输入 'y' 确认转换，其他任意键取消: ")
    
    if user_input.lower() == 'y':
        print(f"开始转换字段 {field_name}...")
        
        # 导入转换器类
        from es_field_type_converter import ESFieldTypeConverter
        
        # 创建转换器实例，指定要转换的字段
        converter = ESFieldTypeConverter()
        converter.field_name = field_name  # 修改要转换的字段名
        converter.temp_index_name = f"{converter.index_name}_temp_{field_name}_{int(time.time())}"
        
        # 执行转换
        converter.run(confirm_swap=True)
    else:
        print("取消转换")


if __name__ == "__main__":
    import time
    
    if check_fields():
        print("\n" + "=" * 80)
        print("如果需要转换 disk_lsof_tmp_delete 字段，请运行:")
        print("python -c \"from check_fields import convert_additional_field; convert_additional_field('disk_lsof_tmp_delete')\"")
