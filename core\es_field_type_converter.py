#!/usr/bin/env python3
"""
Elasticsearch字段类型转换脚本
将psis-collector-harddisk-index索引中的disk_lsof_tmp_deletey字段从text类型转换为integer类型
"""

import json
import time
from datetime import datetime
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError


class ESFieldTypeConverter:
    def __init__(self, es_host="http://***************:9200", index_name="psis-collector-harddisk-index"):
        """
        初始化ES连接和配置
        
        Args:
            es_host (str): Elasticsearch主机地址
            index_name (str): 要修改的索引名称
        """
        self.es_host = es_host
        self.index_name = index_name
        self.temp_index_name = f"{index_name}_temp_{int(time.time())}"
        self.field_name = "disk_lsof_tmp_deletey"
        
        # 初始化ES客户端
        try:
            self.es = Elasticsearch([es_host], timeout=30, max_retries=3, retry_on_timeout=True)
            # 测试连接
            if not self.es.ping():
                raise ConnectionError(f"无法连接到Elasticsearch: {es_host}")
            print(f"✓ 成功连接到Elasticsearch: {es_host}")
        except Exception as e:
            raise ConnectionError(f"连接Elasticsearch失败: {e}")

    def get_current_mapping(self):
        """获取当前索引的映射"""
        try:
            mapping = self.es.indices.get_mapping(index=self.index_name)
            return mapping[self.index_name]['mappings']
        except NotFoundError:
            raise ValueError(f"索引 {self.index_name} 不存在")
        except Exception as e:
            raise RuntimeError(f"获取索引映射失败: {e}")

    def create_new_mapping(self, current_mapping):
        """
        创建新的映射，修改指定字段的类型
        
        Args:
            current_mapping (dict): 当前的映射配置
            
        Returns:
            dict: 新的映射配置
        """
        new_mapping = json.loads(json.dumps(current_mapping))  # 深拷贝
        
        # 修改字段类型
        if 'properties' in new_mapping:
            if self.field_name in new_mapping['properties']:
                print(f"✓ 找到字段 {self.field_name}，当前类型: {new_mapping['properties'][self.field_name].get('type', 'unknown')}")
                new_mapping['properties'][self.field_name] = {
                    "type": "integer",
                    "null_value": 0  # 设置默认值为0
                }
                print(f"✓ 已将字段 {self.field_name} 类型修改为 integer，默认值为 0")
            else:
                # 如果字段不存在，添加新字段
                new_mapping['properties'][self.field_name] = {
                    "type": "integer", 
                    "null_value": 0
                }
                print(f"✓ 添加新字段 {self.field_name}，类型为 integer，默认值为 0")
        else:
            # 如果没有properties，创建一个
            new_mapping['properties'] = {
                self.field_name: {
                    "type": "integer",
                    "null_value": 0
                }
            }
            print(f"✓ 创建properties并添加字段 {self.field_name}")
            
        return new_mapping

    def create_temp_index(self, new_mapping):
        """创建临时索引"""
        try:
            # 获取原索引的设置
            settings = self.es.indices.get_settings(index=self.index_name)
            index_settings = settings[self.index_name]['settings']['index']
            
            # 清理设置中不需要的字段
            clean_settings = {}
            for key, value in index_settings.items():
                if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                    clean_settings[key] = value
            
            # 创建临时索引
            body = {
                "settings": clean_settings,
                "mappings": new_mapping
            }
            
            self.es.indices.create(index=self.temp_index_name, body=body)
            print(f"✓ 成功创建临时索引: {self.temp_index_name}")
            
        except Exception as e:
            raise RuntimeError(f"创建临时索引失败: {e}")

    def reindex_data(self):
        """重新索引数据"""
        try:
            # 构建重新索引的请求体
            reindex_body = {
                "source": {
                    "index": self.index_name
                },
                "dest": {
                    "index": self.temp_index_name
                },
                "script": {
                    "source": f"""
                    if (ctx._source.{self.field_name} != null) {{
                        try {{
                            ctx._source.{self.field_name} = Integer.parseInt(ctx._source.{self.field_name}.toString());
                        }} catch (NumberFormatException e) {{
                            ctx._source.{self.field_name} = 0;
                        }}
                    }} else {{
                        ctx._source.{self.field_name} = 0;
                    }}
                    """,
                    "lang": "painless"
                }
            }
            
            print("开始重新索引数据...")
            response = self.es.reindex(body=reindex_body, wait_for_completion=True, timeout="30m")
            
            if response.get('failures'):
                print(f"⚠ 重新索引过程中有失败记录: {response['failures']}")
            
            print(f"✓ 重新索引完成，处理了 {response.get('total', 0)} 条记录")
            return response
            
        except Exception as e:
            raise RuntimeError(f"重新索引失败: {e}")

    def swap_indices(self):
        """交换索引（删除原索引，重命名临时索引）"""
        try:
            # 删除原索引
            self.es.indices.delete(index=self.index_name)
            print(f"✓ 已删除原索引: {self.index_name}")

            # 真正重命名临时索引
            self.rename_temp_to_final()
            print(f"✓ 已将临时索引重命名为 {self.index_name}")

        except Exception as e:
            raise RuntimeError(f"交换索引失败: {e}")

    def rename_temp_to_final(self):
        """将临时索引真正重命名为最终索引名"""
        try:
            # 获取临时索引的设置和映射
            temp_settings_response = self.es.indices.get_settings(index=self.temp_index_name)
            temp_settings = temp_settings_response[self.temp_index_name]['settings']['index']

            temp_mapping_response = self.es.indices.get_mapping(index=self.temp_index_name)
            temp_mapping = temp_mapping_response[self.temp_index_name]['mappings']

            # 清理设置
            clean_settings = {}
            for key, value in temp_settings.items():
                if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                    clean_settings[key] = value

            # 创建最终索引
            create_body = {
                "settings": clean_settings,
                "mappings": temp_mapping
            }

            self.es.indices.create(index=self.index_name, body=create_body)

            # 重新索引数据从临时索引到最终索引
            reindex_body = {
                "source": {"index": self.temp_index_name},
                "dest": {"index": self.index_name}
            }

            self.es.reindex(body=reindex_body, wait_for_completion=True, timeout="30m")

            # 删除临时索引
            self.es.indices.delete(index=self.temp_index_name)

        except Exception as e:
            raise RuntimeError(f"重命名临时索引失败: {e}")

    def cleanup_temp_index(self):
        """清理临时索引"""
        try:
            if self.es.indices.exists(index=self.temp_index_name):
                self.es.indices.delete(index=self.temp_index_name)
                print(f"✓ 已清理临时索引: {self.temp_index_name}")
        except Exception as e:
            print(f"⚠ 清理临时索引失败: {e}")

    def verify_conversion(self):
        """验证转换结果"""
        try:
            # 等待别名生效
            import time
            time.sleep(2)

            # 获取新的映射 - 处理别名情况
            try:
                new_mapping = self.es.indices.get_mapping(index=self.index_name)
                # 获取实际的索引名（可能是别名指向的索引）
                actual_index_name = list(new_mapping.keys())[0]
                field_type = new_mapping[actual_index_name]['mappings']['properties'].get(self.field_name, {}).get('type')

                print(f"✓ 验证结果: 字段 {self.field_name} 当前类型为 {field_type}")
                print(f"✓ 实际索引名: {actual_index_name}")

            except Exception as mapping_error:
                print(f"⚠ 获取映射失败: {mapping_error}")
                # 尝试直接查询别名信息
                try:
                    aliases = self.es.indices.get_alias(name=self.index_name)
                    print(f"✓ 别名 {self.index_name} 指向的索引: {list(aliases.keys())}")
                except Exception as alias_error:
                    print(f"⚠ 获取别名信息失败: {alias_error}")
                return

            # 获取一些样本数据验证
            try:
                search_body = {
                    "size": 5,
                    "_source": [self.field_name],
                    "query": {"match_all": {}}
                }

                response = self.es.search(index=self.index_name, body=search_body)
                print("✓ 样本数据验证:")
                for hit in response['hits']['hits']:
                    source = hit['_source']
                    field_value = source.get(self.field_name, "未设置")
                    print(f"  文档ID: {hit['_id']}, {self.field_name}: {field_value} (类型: {type(field_value).__name__})")

            except Exception as search_error:
                print(f"⚠ 样本数据查询失败: {search_error}")

        except Exception as e:
            print(f"⚠ 验证过程出错: {e}")

    def run(self, confirm_swap=True):
        """
        执行完整的字段类型转换流程
        
        Args:
            confirm_swap (bool): 是否需要确认交换索引
        """
        try:
            print(f"开始转换索引 {self.index_name} 中字段 {self.field_name} 的类型...")
            print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 60)
            
            # 1. 获取当前映射
            print("1. 获取当前索引映射...")
            current_mapping = self.get_current_mapping()
            
            # 2. 创建新映射
            print("2. 创建新的映射配置...")
            new_mapping = self.create_new_mapping(current_mapping)
            
            # 3. 创建临时索引
            print("3. 创建临时索引...")
            self.create_temp_index(new_mapping)
            
            # 4. 重新索引数据
            print("4. 重新索引数据...")
            reindex_result = self.reindex_data()
            
            # 5. 确认交换索引
            if confirm_swap:
                user_input = input(f"\n是否要将临时索引替换原索引？这将删除原索引 {self.index_name} (y/N): ")
                if user_input.lower() != 'y':
                    print("操作已取消，临时索引保留为:", self.temp_index_name)
                    return
            
            # 6. 交换索引
            print("5. 交换索引...")
            self.swap_indices()
            
            # 7. 验证结果
            print("6. 验证转换结果...")
            self.verify_conversion()
            
            print("-" * 60)
            print("✓ 字段类型转换完成！")
            
        except Exception as e:
            print(f"❌ 转换过程失败: {e}")
            print("正在清理临时索引...")
            self.cleanup_temp_index()
            raise


def main():
    """主函数"""
    try:
        # 创建转换器实例
        converter = ESFieldTypeConverter()
        
        # 执行转换
        converter.run(confirm_swap=True)
        
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
