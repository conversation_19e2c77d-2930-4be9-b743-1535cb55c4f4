# 验证脚本

这个目录包含用于验证操作结果的脚本。

## 文件说明

### verify_conversion.py
- **功能**: 验证字段类型转换结果
- **检查**: 字段类型、默认值、数据完整性
- **使用**: `python verify_conversion.py`

### verify_deleted_fields.py
- **功能**: 验证字段删除结果
- **检查**: 字段是否从映射和数据中删除
- **使用**: `python verify_deleted_fields.py`

### verify_new_deleted_fields.py
- **功能**: 验证最新删除的字段
- **检查**: 针对特定字段列表的验证
- **使用**: `python verify_new_deleted_fields.py`

## 使用建议

1. **操作后验证**: 每次重要操作后运行相应验证脚本
2. **定期检查**: 定期验证索引状态
3. **问题排查**: 出现问题时用于诊断
