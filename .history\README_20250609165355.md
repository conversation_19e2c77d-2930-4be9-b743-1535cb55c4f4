# Elasticsearch字段操作脚本集合

这个项目包含用于Elasticsearch索引字段操作的完整Python脚本集合，支持字段类型转换、字段删除、字段重命名等操作。

## 项目概述

针对ES服务器 `http://***************:9200` 中的 `psis-collector-harddisk-index` 索引进行各种字段操作。

## 📁 项目结构

```
ES_operation/
├── 📂 core/                    # 核心功能脚本（主要使用）
│   ├── delete_fields_config.py    # 🌟 字段删除（推荐）
│   ├── es_field_type_converter.py # 字段类型转换
│   └── README.md
├── 📂 utils/                   # 工具和辅助脚本
│   ├── check_fields.py            # 检查字段信息
│   ├── check_index_status.py      # 检查索引状态
│   ├── rename_index_final.py      # 索引重命名
│   └── README.md
├── 📂 verify/                  # 验证脚本
│   ├── verify_conversion.py       # 验证字段转换
│   ├── verify_deleted_fields.py   # 验证字段删除
│   ├── verify_new_deleted_fields.py # 验证新删除字段
│   └── README.md
├── 📂 archive/                 # 历史版本（不推荐使用）
│   ├── delete_fields_complete.py  # 旧版字段删除
│   ├── simple_es_converter.py     # 简化版转换器
│   ├── ... (其他历史文件)
│   └── README.md
├── README.md                   # 本文件
└── requirements.txt            # 依赖包
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 常用操作

#### 删除字段（最常用）🌟
```bash
# 1. 编辑配置文件
nano core/delete_fields_config.py

# 2. 修改要删除的字段列表
FIELDS_TO_DELETE = [
    "field_name_1",
    "field_name_2",
    # 添加更多字段...
]

# 3. 运行脚本
python core/delete_fields_config.py

# 4. 验证结果
python verify/verify_deleted_fields.py
```

#### 字段类型转换
```bash
# 运行转换脚本
python core/es_field_type_converter.py

# 验证转换结果
python verify/verify_conversion.py
```

#### 检查索引状态
```bash
# 检查字段信息
python utils/check_fields.py

# 检查索引状态
python utils/check_index_status.py
```

## 📋 详细使用指南

### 核心功能 (core/)

#### 🌟 delete_fields_config.py - 交互式字段删除（推荐）
**功能**: 交互式删除Elasticsearch索引中的指定字段
**特点**: 运行时输入、批量删除、字段搜索、保持原索引名

**支持的输入方式**:
1. **逐个输入**: 适合少量字段
2. **批量输入**: 逗号分隔，如 `field1,field2,field3`
3. **查看所有字段**: 显示索引中的所有字段
4. **搜索匹配**: 按关键词搜索相关字段
5. **配置文件预设**: 使用预设的字段列表

**使用方法**:
```bash
python core/delete_fields_config.py
# 按提示选择输入方式和字段
```

**批量输入示例**:
```
字段列表: tmpfs_runuser1009Used,tmpfs_runuser1009Usage,tmpfs_runuser1009Size,tmpfs_runuser1009Avail,tmpfs_runuser1009Mounted
```

#### es_field_type_converter.py - 字段类型转换
**功能**: 转换字段类型（如text转integer）
**特点**: 包含数据转换逻辑，支持默认值设置

**使用方法**:
```bash
python core/es_field_type_converter.py
```

### 工具功能 (utils/)

#### check_fields.py - 交互式字段信息检查
**功能**: 用户指定字段进行详细信息检查
**特点**: 支持批量输入、搜索字段、样本数据分析、优化建议
**用途**: 了解字段类型、使用情况、性能优化
**输入方式**: 逐个输入、逗号分隔批量输入、查看所有字段、搜索匹配

#### check_index_status.py - 索引状态检查
**功能**: 检查和修复索引状态问题
**用途**: 解决别名问题、清理临时索引

#### rename_index_interactive.py - 交互式索引重命名
**功能**: 将任意索引重命名为指定名称
**特点**: 支持用户输入当前索引名和目标索引名
**用途**: 清理临时索引名称，重命名索引

### 验证功能 (verify/)

#### verify_conversion.py - 验证字段转换
**功能**: 验证字段类型转换是否成功
**检查**: 字段类型、默认值、数据完整性

#### verify_deleted_fields.py - 验证字段删除
**功能**: 验证字段是否成功删除
**检查**: 映射中是否删除、数据中是否清除

## 已完成的操作历史

### ✅ 第一阶段：字段类型转换
- 将 `disk_lsof_tmp_deletey` 从 `text` 转换为 `integer`
- 设置默认值为 `0`

### ✅ 第二阶段：字段重命名
- 删除原 `disk_lsof_tmp_delete` 字段
- 将 `disk_lsof_tmp_deletey` 重命名为 `disk_lsof_tmp_delete`

### ✅ 第三阶段：字段删除（第一批）
删除了以下字段：
- `tmpfs_runuser500Used`
- `tmpfs_runuser500Usage`
- `tmpfs_runuser500Size`
- `tmpfs_runuser500Avail`
- `tmpfs_runuser500Mounted`

### ✅ 第四阶段：字段删除（第二批）
删除了以下字段：
- `tmpfs_runuser1009Used`
- `tmpfs_runuser1009Usage`
- `tmpfs_runuser1009Size`
- `tmpfs_runuser1009Avail`
- `tmpfs_runuser1009Mounted`

## 当前索引状态

- **索引名称**: `psis-collector-harddisk-index`
- **文档总数**: 127,793 条
- **字段总数**: 79 个
- **重要字段**: `disk_lsof_tmp_delete` (integer 类型，默认值 0)

## 操作流程

所有脚本都遵循相同的安全操作流程：

1. **连接验证** - 测试ES连接
2. **状态检查** - 检查索引和字段状态
3. **创建临时索引** - 使用新配置创建临时索引
4. **数据迁移** - 安全地迁移数据
5. **验证** - 验证操作结果
6. **交换索引** - 用户确认后交换索引
7. **清理** - 清理临时文件

## 配置说明

### ES连接配置
```python
ES_CONFIG = {
    "host": "http://***************:9200",
    "index_name": "psis-collector-harddisk-index",
    "timeout": 30,
    "max_retries": 3
}
```

### 操作配置
```python
OPERATION_CONFIG = {
    "confirm_before_swap": True,  # 交换前确认
    "auto_cleanup": True,         # 自动清理
    "show_progress": True,        # 显示进度
    "verify_samples": 3           # 验证样本数
}
```

## 安全特性

- ✅ **数据备份**: 操作前创建临时索引备份
- ✅ **用户确认**: 关键操作前需要用户确认
- ✅ **回滚机制**: 失败时自动清理临时索引
- ✅ **验证机制**: 多层验证确保操作正确
- ✅ **进度监控**: 实时显示操作进度

## 故障排除

1. **连接失败**: 检查ES服务器地址和网络
2. **权限不足**: 确保有索引操作权限
3. **内存不足**: 调整ES内存设置
4. **文档数量不匹配**: 检查是否有并发写入

## 最佳实践

1. **测试环境验证**: 先在测试环境验证脚本
2. **备份数据**: 重要操作前备份数据
3. **分批操作**: 大量字段分批删除
4. **监控资源**: 注意ES集群资源使用情况

## 验证命令

```bash
# 检查索引映射
curl -X GET "http://***************:9200/psis-collector-harddisk-index/_mapping"

# 检查索引统计
curl -X GET "http://***************:9200/psis-collector-harddisk-index/_stats"

# 检查文档数量
curl -X GET "http://***************:9200/psis-collector-harddisk-index/_count"
```
