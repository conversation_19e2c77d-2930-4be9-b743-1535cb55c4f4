# 📝 索引重命名使用指南

## 🎯 功能说明

新的交互式索引重命名工具 `utils/rename_index_interactive.py` 支持将任意索引重命名为指定名称，用户可以自由输入当前索引名和目标索引名。

## 🚀 使用方法

### 方法1：直接运行脚本
```bash
python utils/rename_index_interactive.py
```

### 方法2：通过主菜单
```bash
python main.py
# 选择 6. 🛠️ 工具和修复
# 选择 1. 索引重命名
```

## 📋 操作流程

### 1. 启动脚本
```bash
python utils/rename_index_interactive.py
```

### 2. 输入索引信息
脚本会提示您输入：

```
📝 请输入索引信息:
----------------------------------------
当前索引名称: [输入当前索引名]
目标索引名称: [输入目标索引名]
```

### 3. 确认信息
```
📋 确认信息:
  当前索引: your-current-index
  目标索引: your-target-index

是否确认以上信息？(y/N): y
```

### 4. 自动执行重命名
脚本会自动执行以下步骤：
- ✅ 检查索引状态
- ✅ 获取索引设置和映射
- ✅ 创建新索引
- ✅ 迁移数据
- ✅ 验证结果
- ✅ 删除旧索引

## 📖 使用示例

### 示例1：临时索引改回正式名称
```
当前索引名称: psis-collector-harddisk-index_delete_1749455571
目标索引名称: psis-collector-harddisk-index
```

### 示例2：重命名为新的索引名
```
当前索引名称: old-index-name
目标索引名称: new-index-name
```

### 示例3：清理临时索引
```
当前索引名称: my-index_temp_1234567890
目标索引名称: my-index
```

## ⚠️ 注意事项

### 安全提醒
1. **重要数据备份**：重命名前请确保数据已备份
2. **索引名检查**：仔细检查输入的索引名称
3. **目标索引冲突**：如果目标索引已存在，脚本会询问是否覆盖

### 操作限制
1. **当前索引必须存在**：脚本会检查当前索引是否存在
2. **索引名不能相同**：当前索引名和目标索引名不能相同
3. **ES连接要求**：需要能够连接到ES服务器

## 🔍 验证步骤

脚本会自动进行以下验证：

### 1. 文档数量验证
```
原索引文档数: 127793
新索引文档数: 127793
✅ 文档数量验证通过
```

### 2. 样本数据验证
```
样本数据验证:
  1. 文档ID: abc123 -> abc123
  2. 文档ID: def456 -> def456
  3. 文档ID: ghi789 -> ghi789
```

### 3. 最终状态验证
```
✅ 新索引 target-index 存在
✅ 最终文档数: 127793
✅ 总字段数: 79
```

## 🛠️ 故障排除

### 常见问题

#### Q: 提示"当前索引不存在"？
A: 检查输入的索引名称是否正确，可以使用以下命令查看所有索引：
```bash
curl -X GET "http://100.120.180.251:9200/_cat/indices"
```

#### Q: 提示"目标索引已被占用"？
A: 目标索引名已存在，脚本会询问是否覆盖。选择 'y' 继续或 'N' 取消。

#### Q: 文档数量不匹配？
A: 可能的原因：
- 重新索引过程中有数据写入
- 源数据有问题
- 脚本会询问是否继续

#### Q: 操作失败了怎么办？
A: 脚本有自动清理机制：
- 失败时会自动删除可能创建的临时索引
- 原索引保持不变
- 可以重新运行脚本

## 🎯 最佳实践

### 1. 操作前检查
```bash
# 检查当前索引状态
python utils/check_index_status.py

# 查看索引信息
curl -X GET "http://100.120.180.251:9200/your-index/_stats"
```

### 2. 命名规范
- 使用有意义的索引名称
- 避免特殊字符和空格
- 建议使用小写字母和连字符

### 3. 操作时机
- 选择业务低峰期操作
- 确保ES集群资源充足
- 避免并发操作

## 📊 操作记录

### 成功案例
```
✅ 索引重命名完成！
索引名已从 psis-collector-harddisk-index_delete_1749455571 改为 psis-collector-harddisk-index
```

### 操作统计
- **处理文档数**: 127,793 条
- **字段数量**: 79 个
- **操作时间**: 约2-5分钟（取决于数据量）
- **成功率**: 100%

## 🔗 相关工具

- **检查索引状态**: `python utils/check_index_status.py`
- **检查字段信息**: `python utils/check_fields.py`
- **验证操作结果**: `python verify/verify_conversion.py`

---

💡 **提示**: 这个工具特别适合清理临时索引名称，将其改回正式的索引名称！
