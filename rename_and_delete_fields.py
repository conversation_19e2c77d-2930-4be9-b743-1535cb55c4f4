#!/usr/bin/env python3
"""
删除 disk_lsof_tmp_delete 字段，将 disk_lsof_tmp_deletey 重命名为 disk_lsof_tmp_delete
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


def rename_and_delete_fields():
    """重命名字段并删除旧字段"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    OLD_FIELD = "disk_lsof_tmp_delete"  # 要删除的字段
    RENAME_FROM = "disk_lsof_tmp_deletey"  # 要重命名的字段
    RENAME_TO = "disk_lsof_tmp_delete"  # 新的字段名
    TEMP_INDEX = f"{INDEX_NAME}_rename_{int(time.time())}"
    
    print(f"开始字段操作:")
    print(f"  - 删除字段: {OLD_FIELD}")
    print(f"  - 重命名字段: {RENAME_FROM} -> {RENAME_TO}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 获取当前索引的实际名称（可能是别名）
        aliases = es.indices.get_alias(name=INDEX_NAME)
        if aliases:
            actual_index = list(aliases.keys())[0]
            print(f"✓ 索引 {INDEX_NAME} 是别名，指向: {actual_index}")
        else:
            actual_index = INDEX_NAME
            print(f"✓ 索引 {INDEX_NAME} 是直接索引")
        
        # 2. 获取原索引映射
        print("获取原索引映射...")
        original_mapping = es.indices.get_mapping(index=INDEX_NAME)
        mapping = original_mapping[actual_index]['mappings']
        
        # 3. 检查字段是否存在
        properties = mapping.get('properties', {})
        
        if OLD_FIELD in properties:
            print(f"✓ 找到要删除的字段: {OLD_FIELD} (类型: {properties[OLD_FIELD].get('type', '未知')})")
        else:
            print(f"⚠ 要删除的字段 {OLD_FIELD} 不存在")
        
        if RENAME_FROM in properties:
            print(f"✓ 找到要重命名的字段: {RENAME_FROM} (类型: {properties[RENAME_FROM].get('type', '未知')})")
            rename_field_config = properties[RENAME_FROM]
        else:
            print(f"❌ 要重命名的字段 {RENAME_FROM} 不存在")
            return False
        
        # 4. 创建新的映射
        print("创建新的映射配置...")
        new_mapping = {
            "properties": {}
        }
        
        # 复制所有字段，除了要删除的字段
        for field_name, field_config in properties.items():
            if field_name == OLD_FIELD:
                print(f"  - 跳过字段: {field_name} (将被删除)")
                continue
            elif field_name == RENAME_FROM:
                # 重命名字段
                new_mapping['properties'][RENAME_TO] = field_config
                print(f"  - 重命名字段: {field_name} -> {RENAME_TO}")
            else:
                # 保持其他字段不变
                new_mapping['properties'][field_name] = field_config
        
        print(f"✓ 新映射包含 {len(new_mapping['properties'])} 个字段")
        
        # 5. 获取原索引设置
        settings = es.indices.get_settings(index=INDEX_NAME)
        index_settings = settings[actual_index]['settings']['index']
        
        # 清理设置
        clean_settings = {k: v for k, v in index_settings.items() 
                         if k not in ['creation_date', 'uuid', 'version', 'provided_name']}
        
        # 6. 创建临时索引
        print(f"创建临时索引 {TEMP_INDEX}...")
        es.indices.create(
            index=TEMP_INDEX,
            body={
                "settings": clean_settings,
                "mappings": new_mapping
            }
        )
        print("✓ 临时索引创建成功")
        
        # 7. 重新索引数据
        print("重新索引数据...")
        reindex_body = {
            "source": {"index": INDEX_NAME},
            "dest": {"index": TEMP_INDEX},
            "script": {
                "source": f"""
                // 删除 {OLD_FIELD} 字段
                if (ctx._source.containsKey('{OLD_FIELD}')) {{
                    ctx._source.remove('{OLD_FIELD}');
                }}

                // 重命名 {RENAME_FROM} 为 {RENAME_TO}
                if (ctx._source.containsKey('{RENAME_FROM}')) {{
                    ctx._source['{RENAME_TO}'] = ctx._source['{RENAME_FROM}'];
                    ctx._source.remove('{RENAME_FROM}');
                }}
                """,
                "lang": "painless"
            }
        }

        # 启动异步重新索引
        print("启动异步重新索引任务...")
        result = es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
        task_id = result['task']
        print(f"✓ 重新索引任务已启动，任务ID: {task_id}")

        # 监控重新索引进度
        print("监控重新索引进度...")
        while True:
            try:
                task_status = es.tasks.get(task_id=task_id)
                task_info = task_status['task']

                if task_status['completed']:
                    print("✓ 重新索引任务完成")
                    response = task_status.get('response', {})
                    print(f"  处理文档数: {response.get('total', 0)}")
                    print(f"  创建文档数: {response.get('created', 0)}")
                    print(f"  更新文档数: {response.get('updated', 0)}")
                    if response.get('failures'):
                        print(f"  失败记录: {len(response['failures'])}")
                    break
                else:
                    status = task_info.get('status', {})
                    total = status.get('total', 0)
                    created = status.get('created', 0)
                    if total > 0:
                        progress = (created / total) * 100
                        print(f"  进度: {created}/{total} ({progress:.1f}%)")
                    else:
                        print(f"  已处理: {created} 条记录")

                    time.sleep(10)  # 等待10秒后再次检查

            except Exception as task_error:
                print(f"⚠ 获取任务状态失败: {task_error}")
                time.sleep(10)
        
        # 8. 验证新索引
        print("验证新索引...")
        new_mapping_check = es.indices.get_mapping(index=TEMP_INDEX)
        new_properties = new_mapping_check[TEMP_INDEX]['mappings']['properties']
        
        print("字段验证结果:")
        if OLD_FIELD in new_properties:
            print(f"  ❌ {OLD_FIELD} 仍然存在（删除失败）")
        else:
            print(f"  ✅ {OLD_FIELD} 已成功删除")
        
        if RENAME_FROM in new_properties:
            print(f"  ❌ {RENAME_FROM} 仍然存在（重命名失败）")
        else:
            print(f"  ✅ {RENAME_FROM} 已成功删除")
        
        if RENAME_TO in new_properties:
            field_type = new_properties[RENAME_TO].get('type', '未知')
            print(f"  ✅ {RENAME_TO} 已存在，类型: {field_type}")
        else:
            print(f"  ❌ {RENAME_TO} 不存在（重命名失败）")
        
        # 检查样本数据
        print("\n样本数据验证:")
        search_body = {
            "size": 5,
            "_source": [RENAME_TO, OLD_FIELD, RENAME_FROM],
            "query": {"match_all": {}}
        }
        
        response = es.search(index=TEMP_INDEX, body=search_body)
        for i, hit in enumerate(response['hits']['hits'], 1):
            source = hit['_source']
            print(f"  {i}. 文档ID: {hit['_id']}")
            
            # 检查新字段
            if RENAME_TO in source:
                value = source[RENAME_TO]
                print(f"     {RENAME_TO}: {value} (类型: {type(value).__name__})")
            else:
                print(f"     {RENAME_TO}: 不存在")
            
            # 检查旧字段是否还存在
            if OLD_FIELD in source:
                print(f"     ⚠ {OLD_FIELD}: 仍然存在")
            if RENAME_FROM in source:
                print(f"     ⚠ {RENAME_FROM}: 仍然存在")
        
        # 9. 确认交换索引
        print("\n" + "=" * 80)
        user_input = input(f"是否要将临时索引替换原索引？这将删除当前的 {INDEX_NAME} (y/N): ")
        
        if user_input.lower() != 'y':
            print(f"操作已取消，临时索引保留为: {TEMP_INDEX}")
            print("您可以稍后手动验证并交换索引")
            return True
        
        # 10. 交换索引
        print("交换索引...")
        
        # 删除原别名
        if aliases:
            es.indices.delete_alias(index=actual_index, name=INDEX_NAME)
            print(f"✓ 已删除别名 {INDEX_NAME}")
            
            # 删除原索引
            es.indices.delete(index=actual_index)
            print(f"✓ 已删除原索引 {actual_index}")
        else:
            es.indices.delete(index=INDEX_NAME)
            print(f"✓ 已删除原索引 {INDEX_NAME}")
        
        # 创建新别名
        es.indices.put_alias(index=TEMP_INDEX, name=INDEX_NAME)
        print(f"✓ 已将临时索引 {TEMP_INDEX} 设置别名为 {INDEX_NAME}")
        
        # 11. 最终验证
        print("最终验证...")
        final_mapping = es.indices.get_mapping(index=INDEX_NAME)
        final_actual_index = list(final_mapping.keys())[0]
        final_properties = final_mapping[final_actual_index]['mappings']['properties']
        
        print("最终字段状态:")
        if OLD_FIELD in final_properties:
            print(f"  ❌ {OLD_FIELD} 仍然存在")
        else:
            print(f"  ✅ {OLD_FIELD} 已成功删除")
        
        if RENAME_FROM in final_properties:
            print(f"  ❌ {RENAME_FROM} 仍然存在")
        else:
            print(f"  ✅ {RENAME_FROM} 已成功删除")
        
        if RENAME_TO in final_properties:
            field_type = final_properties[RENAME_TO].get('type', '未知')
            null_value = final_properties[RENAME_TO].get('null_value', '未设置')
            print(f"  ✅ {RENAME_TO} 存在，类型: {field_type}，默认值: {null_value}")
        else:
            print(f"  ❌ {RENAME_TO} 不存在")
        
        print("=" * 80)
        print("✅ 字段操作完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        
        # 清理临时索引
        try:
            if es.indices.exists(index=TEMP_INDEX):
                es.indices.delete(index=TEMP_INDEX)
                print(f"已清理临时索引 {TEMP_INDEX}")
        except:
            pass
        
        return False


if __name__ == "__main__":
    rename_and_delete_fields()
