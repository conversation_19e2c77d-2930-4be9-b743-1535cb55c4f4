#!/usr/bin/env python3
"""
完善的Elasticsearch字段删除脚本
支持删除指定字段列表，包含完整的验证和清理功能
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


class ESFieldDeleter:
    def __init__(self, es_host="http://***************:9200", index_name="psis-collector-harddisk-index"):
        """
        初始化ES字段删除器
        
        Args:
            es_host (str): Elasticsearch主机地址
            index_name (str): 要操作的索引名称
        """
        self.es_host = es_host
        self.index_name = index_name
        self.temp_index_name = None
        
        # 初始化ES客户端
        try:
            self.es = Elasticsearch([es_host], timeout=30, max_retries=3, retry_on_timeout=True)
            if not self.es.ping():
                raise ConnectionError(f"无法连接到Elasticsearch: {es_host}")
            print(f"✓ 成功连接到Elasticsearch: {es_host}")
        except Exception as e:
            raise ConnectionError(f"连接Elasticsearch失败: {e}")

    def delete_fields(self, fields_to_delete):
        """
        删除指定字段列表
        
        Args:
            fields_to_delete (list): 要删除的字段名列表
            
        Returns:
            bool: 操作是否成功
        """
        if not fields_to_delete:
            print("❌ 字段列表为空")
            return False
        
        self.temp_index_name = f"{self.index_name}_delete_{int(time.time())}"
        
        print(f"开始删除字段操作:")
        print(f"  索引: {self.index_name}")
        print(f"  要删除的字段: {', '.join(fields_to_delete)}")
        print(f"  临时索引: {self.temp_index_name}")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 1. 检查索引状态
            if not self._check_index_status():
                return False
            
            # 2. 分析要删除的字段
            existing_fields = self._analyze_fields(fields_to_delete)
            if not existing_fields:
                return False
            
            # 3. 创建新映射
            new_mapping = self._create_new_mapping(existing_fields)
            if not new_mapping:
                return False
            
            # 4. 创建临时索引
            if not self._create_temp_index(new_mapping):
                return False
            
            # 5. 重新索引数据
            if not self._reindex_data(existing_fields):
                return False
            
            # 6. 验证新索引
            if not self._verify_new_index(existing_fields):
                return False
            
            # 7. 交换索引
            if not self._swap_indices():
                return False
            
            # 8. 最终验证
            self._final_verification(existing_fields)
            
            print("=" * 80)
            print("✅ 字段删除操作完成！")
            print(f"已成功删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            self._cleanup_temp_index()
            return False

    def _check_index_status(self):
        """检查索引状态"""
        print("1. 检查索引状态...")
        
        if not self.es.indices.exists(index=self.index_name):
            print(f"❌ 索引 {self.index_name} 不存在")
            return False
        
        print(f"✓ 索引 {self.index_name} 存在")
        
        # 检查是否是别名
        try:
            aliases = self.es.indices.get_alias(name=self.index_name)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"✓ {self.index_name} 是别名，指向: {actual_index}")
                self.actual_index = actual_index
            else:
                print(f"✓ {self.index_name} 是直接索引")
                self.actual_index = self.index_name
        except:
            self.actual_index = self.index_name
        
        return True

    def _analyze_fields(self, fields_to_delete):
        """分析要删除的字段"""
        print("2. 分析要删除的字段...")
        
        # 获取当前映射
        mapping_response = self.es.indices.get_mapping(index=self.index_name)
        current_mapping = mapping_response[self.actual_index]['mappings']
        properties = current_mapping.get('properties', {})
        
        existing_fields = []
        missing_fields = []
        
        for field in fields_to_delete:
            if field in properties:
                existing_fields.append(field)
                field_type = properties[field].get('type', '未知')
                print(f"✓ 找到字段: {field} (类型: {field_type})")
            else:
                missing_fields.append(field)
                print(f"⚠ 字段不存在: {field}")
        
        if not existing_fields:
            print("❌ 没有找到任何要删除的字段")
            return None
        
        print(f"\n将删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
        if missing_fields:
            print(f"跳过 {len(missing_fields)} 个不存在的字段: {', '.join(missing_fields)}")
        
        # 分析字段使用情况
        print("\n分析字段使用情况...")
        for field in existing_fields:
            try:
                exists_query = {
                    "size": 0,
                    "query": {"exists": {"field": field}}
                }
                result = self.es.search(index=self.index_name, body=exists_query)
                doc_count = result['hits']['total']['value'] if isinstance(result['hits']['total'], dict) else result['hits']['total']
                print(f"  {field}: {doc_count} 个文档包含此字段")
            except Exception as e:
                print(f"  {field}: 无法分析 ({e})")
        
        self.current_mapping = current_mapping
        self.properties = properties
        return existing_fields

    def _create_new_mapping(self, existing_fields):
        """创建新的映射配置"""
        print("\n3. 创建新的映射配置...")
        
        new_mapping = {"properties": {}}
        deleted_count = 0
        kept_count = 0
        
        for field_name, field_config in self.properties.items():
            if field_name in existing_fields:
                print(f"  - 删除字段: {field_name}")
                deleted_count += 1
            else:
                new_mapping['properties'][field_name] = field_config
                kept_count += 1
        
        print(f"✓ 新映射包含 {kept_count} 个字段 (删除了 {deleted_count} 个字段)")
        return new_mapping

    def _create_temp_index(self, new_mapping):
        """创建临时索引"""
        print("\n4. 创建临时索引...")
        
        try:
            # 获取索引设置
            settings_response = self.es.indices.get_settings(index=self.index_name)
            current_settings = settings_response[self.actual_index]['settings']['index']
            
            # 清理设置
            clean_settings = {}
            for key, value in current_settings.items():
                if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                    clean_settings[key] = value
            
            # 创建临时索引
            create_body = {
                "settings": clean_settings,
                "mappings": new_mapping
            }
            
            self.es.indices.create(index=self.temp_index_name, body=create_body)
            print(f"✓ 临时索引 {self.temp_index_name} 创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建临时索引失败: {e}")
            return False

    def _reindex_data(self, existing_fields):
        """重新索引数据"""
        print("\n5. 重新索引数据...")
        
        try:
            # 构建删除字段的脚本
            script_lines = []
            for field in existing_fields:
                script_lines.append(f'if (ctx._source.containsKey("{field}")) {{ ctx._source.remove("{field}"); }}')
            
            script_source = "\n".join(script_lines)
            
            reindex_body = {
                "source": {"index": self.index_name},
                "dest": {"index": self.temp_index_name},
                "script": {
                    "source": script_source,
                    "lang": "painless"
                }
            }
            
            # 启动异步重新索引
            result = self.es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
            task_id = result['task']
            print(f"✓ 数据迁移任务已启动，任务ID: {task_id}")
            
            # 监控进度
            print("监控数据迁移进度...")
            while True:
                try:
                    task_status = self.es.tasks.get(task_id=task_id)
                    
                    if task_status['completed']:
                        print("✓ 数据迁移任务完成")
                        response = task_status.get('response', {})
                        print(f"  处理文档数: {response.get('total', 0)}")
                        print(f"  创建文档数: {response.get('created', 0)}")
                        if response.get('failures'):
                            print(f"  失败记录: {len(response['failures'])}")
                        break
                    else:
                        task_info = task_status['task']
                        status = task_info.get('status', {})
                        total = status.get('total', 0)
                        created = status.get('created', 0)
                        if total > 0:
                            progress = (created / total) * 100
                            print(f"  进度: {created}/{total} ({progress:.1f}%)")
                        else:
                            print(f"  已处理: {created} 条记录")
                        
                        time.sleep(5)
                        
                except Exception as task_error:
                    print(f"⚠ 获取任务状态失败: {task_error}")
                    time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ 重新索引失败: {e}")
            return False

    def _verify_new_index(self, existing_fields):
        """验证新索引"""
        print("\n6. 验证新索引...")
        
        try:
            # 检查文档数量
            old_count = self.es.count(index=self.index_name)['count']
            new_count = self.es.count(index=self.temp_index_name)['count']
            
            print(f"  原索引文档数: {old_count}")
            print(f"  新索引文档数: {new_count}")
            
            if old_count == new_count:
                print("✅ 文档数量验证通过")
            else:
                print(f"⚠ 文档数量不匹配，差异: {old_count - new_count}")
                user_continue = input("文档数量不匹配，是否继续？(y/N): ")
                if user_continue.lower() != 'y':
                    return False
            
            # 检查字段是否已删除
            new_mapping_check = self.es.indices.get_mapping(index=self.temp_index_name)
            new_properties = new_mapping_check[self.temp_index_name]['mappings']['properties']
            
            print("字段删除验证:")
            all_deleted = True
            for field in existing_fields:
                if field in new_properties:
                    print(f"  ❌ {field} 仍然存在（删除失败）")
                    all_deleted = False
                else:
                    print(f"  ✅ {field} 已成功删除")
            
            if not all_deleted:
                print("❌ 部分字段删除失败")
                return False
            
            # 检查样本数据
            print("\n样本数据验证:")
            search_body = {
                "size": 3,
                "_source": existing_fields + ["disk_lsof_tmp_delete"],
                "query": {"match_all": {}}
            }
            
            try:
                new_response = self.es.search(index=self.temp_index_name, body=search_body)
                
                for i, hit in enumerate(new_response['hits']['hits'], 1):
                    print(f"  {i}. 文档ID: {hit['_id']}")
                    source = hit['_source']
                    
                    # 检查删除的字段
                    deleted_fields_found = [field for field in existing_fields if field in source]
                    
                    if deleted_fields_found:
                        print(f"     ❌ 仍包含已删除字段: {', '.join(deleted_fields_found)}")
                        all_deleted = False
                    else:
                        print(f"     ✅ 已删除字段不存在")
                    
                    # 检查保留字段
                    if 'disk_lsof_tmp_delete' in source:
                        print(f"     ✅ 保留字段 disk_lsof_tmp_delete: {source['disk_lsof_tmp_delete']}")
                        
            except Exception as search_error:
                print(f"⚠ 样本数据验证失败: {search_error}")
            
            return all_deleted
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

    def _swap_indices(self):
        """交换索引"""
        print("\n7. 交换索引...")
        
        try:
            # 确认操作
            print("验证摘要:")
            new_mapping_check = self.es.indices.get_mapping(index=self.temp_index_name)
            new_properties = new_mapping_check[self.temp_index_name]['mappings']['properties']
            old_count = self.es.count(index=self.index_name)['count']
            new_count = self.es.count(index=self.temp_index_name)['count']
            
            print(f"  - 保留字段数: {len(new_properties)}")
            print(f"  - 文档数变化: {old_count} -> {new_count}")
            
            user_input = input("\n是否用新索引替换原索引？(y/N): ")
            if user_input.lower() != 'y':
                print(f"操作已取消，临时索引保留为: {self.temp_index_name}")
                return True
            
            # 删除原索引或别名
            if hasattr(self, 'actual_index') and self.actual_index != self.index_name:
                # 是别名
                self.es.indices.delete_alias(index=self.actual_index, name=self.index_name)
                print(f"✓ 已删除别名 {self.index_name}")
                self.es.indices.delete(index=self.actual_index)
                print(f"✓ 已删除原索引 {self.actual_index}")
            else:
                # 是直接索引
                self.es.indices.delete(index=self.index_name)
                print(f"✓ 已删除原索引 {self.index_name}")
            
            # 重命名临时索引
            self.es.indices.put_alias(index=self.temp_index_name, name=self.index_name)
            print(f"✓ 已将临时索引 {self.temp_index_name} 设置别名为 {self.index_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ 交换索引失败: {e}")
            return False

    def _final_verification(self, existing_fields):
        """最终验证"""
        print("\n8. 最终验证...")
        
        try:
            final_mapping = self.es.indices.get_mapping(index=self.index_name)
            final_actual_index = list(final_mapping.keys())[0]
            final_properties = final_mapping[final_actual_index]['mappings']['properties']
            
            print("最终字段状态:")
            for field in existing_fields:
                if field in final_properties:
                    print(f"  ❌ {field} 仍然存在")
                else:
                    print(f"  ✅ {field} 已成功删除")
            
            # 检查重要字段
            if 'disk_lsof_tmp_delete' in final_properties:
                field_type = final_properties['disk_lsof_tmp_delete'].get('type', '未知')
                print(f"  ✅ disk_lsof_tmp_delete 保留，类型: {field_type}")
            
            final_count = self.es.count(index=self.index_name)['count']
            print(f"✅ 最终文档数: {final_count}")
            print(f"✅ 最终字段数: {len(final_properties)}")
            
        except Exception as e:
            print(f"⚠ 最终验证失败: {e}")

    def _cleanup_temp_index(self):
        """清理临时索引"""
        try:
            if self.temp_index_name and self.es.indices.exists(index=self.temp_index_name):
                self.es.indices.delete(index=self.temp_index_name)
                print(f"已清理临时索引 {self.temp_index_name}")
        except:
            pass


def main():
    """主函数"""
    # 要删除的字段列表
    FIELDS_TO_DELETE = [
        "tmpfs_runuser1009Used",
        "tmpfs_runuser1009Usage",
        "tmpfs_runuser1009Size",
        "tmpfs_runuser1009Avail",
        "tmpfs_runuser1009Mounted"
    ]
    
    try:
        # 创建字段删除器
        deleter = ESFieldDeleter()
        
        # 执行删除操作
        success = deleter.delete_fields(FIELDS_TO_DELETE)
        
        if success:
            print("\n🎉 所有操作成功完成！")
            return 0
        else:
            print("\n❌ 操作失败")
            return 1
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
