#!/usr/bin/env python3
"""
测试索引命名是否正确
验证删除字段和字段类型转换操作后索引名是否保持不变
"""

from elasticsearch import Elasticsearch


def test_index_naming():
    """测试索引命名"""
    
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    
    print("🔍 检查索引命名状态")
    print("=" * 60)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查索引是否存在
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return False
        
        print(f"✓ 索引 {INDEX_NAME} 存在")
        
        # 2. 检查是否是别名还是实际索引
        aliases = None
        try:
            aliases = es.indices.get_alias(name=INDEX_NAME)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"⚠ {INDEX_NAME} 是别名，指向实际索引: {actual_index}")

                # 检查实际索引名是否是临时名称
                if "_temp_" in actual_index or "_delete_" in actual_index or "_rename_" in actual_index:
                    print(f"❌ 实际索引名包含临时标识符，需要重命名")
                    print(f"   建议使用 utils/rename_index_interactive.py 进行重命名")
                    return False
                else:
                    print(f"✅ 实际索引名正常")

            else:
                print(f"✅ {INDEX_NAME} 是直接索引（不是别名）")

        except Exception as e:
            print(f"✅ {INDEX_NAME} 是直接索引（检查别名时出错: {e}）")
            aliases = None
        
        # 3. 检查索引基本信息
        try:
            # 获取文档数量
            doc_count = es.count(index=INDEX_NAME)['count']
            print(f"✓ 文档总数: {doc_count:,}")
            
            # 获取字段数量
            mapping = es.indices.get_mapping(index=INDEX_NAME)
            if aliases:
                actual_index = list(aliases.keys())[0]
                properties = mapping[actual_index]['mappings'].get('properties', {})
            else:
                properties = mapping[INDEX_NAME]['mappings'].get('properties', {})
            
            field_count = len(properties)
            print(f"✓ 字段总数: {field_count}")
            
            # 检查重要字段
            important_fields = ["disk_lsof_tmp_delete", "IP", "Date", "NeName"]
            for field in important_fields:
                if field in properties:
                    field_type = properties[field].get('type', '未知')
                    print(f"✓ 重要字段 {field}: {field_type}")
                else:
                    print(f"⚠ 重要字段 {field}: 不存在")
            
        except Exception as info_error:
            print(f"⚠ 获取索引信息失败: {info_error}")
        
        # 4. 检查是否有临时索引残留
        print("\n检查临时索引残留...")
        try:
            all_indices = es.indices.get(index="*")
            temp_indices = []
            
            for index_name in all_indices.keys():
                if ("psis-collector-harddisk-index" in index_name and 
                    index_name != INDEX_NAME and
                    ("_temp_" in index_name or "_delete_" in index_name or "_rename_" in index_name)):
                    temp_indices.append(index_name)
            
            if temp_indices:
                print(f"⚠ 发现 {len(temp_indices)} 个临时索引:")
                for temp_index in temp_indices:
                    doc_count = es.count(index=temp_index)['count']
                    print(f"  - {temp_index}: {doc_count:,} 条文档")
                
                print("\n建议清理这些临时索引（如果确认不需要）:")
                for temp_index in temp_indices:
                    print(f"  curl -X DELETE 'http://***************:9200/{temp_index}'")
            else:
                print("✅ 没有发现临时索引残留")
                
        except Exception as temp_error:
            print(f"⚠ 检查临时索引失败: {temp_error}")
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("📊 索引状态总结:")
        
        if aliases:
            actual_index = list(aliases.keys())[0]
            if "_temp_" in actual_index or "_delete_" in actual_index or "_rename_" in actual_index:
                print("❌ 索引名称需要修复")
                print(f"   当前: {INDEX_NAME} -> {actual_index}")
                print(f"   建议: 使用重命名工具将 {actual_index} 改为 {INDEX_NAME}")
                return False
            else:
                print("✅ 索引名称正常（使用别名）")
                print(f"   别名: {INDEX_NAME} -> {actual_index}")
        else:
            print("✅ 索引名称正常（直接索引）")
            print(f"   索引: {INDEX_NAME}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False


def main():
    """主函数"""
    success = test_index_naming()
    
    if success:
        print("\n🎉 索引命名检查通过！")
        return 0
    else:
        print("\n⚠ 索引命名需要修复")
        print("\n🔧 修复建议:")
        print("1. 使用重命名工具: python utils/rename_index_interactive.py")
        print("2. 或通过主菜单: python main.py -> 6 -> 1")
        return 1


if __name__ == "__main__":
    exit(main())
