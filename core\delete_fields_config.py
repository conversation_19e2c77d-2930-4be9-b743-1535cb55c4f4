#!/usr/bin/env python3
"""
可配置的Elasticsearch字段删除脚本
通过修改配置来删除不同的字段列表
"""

from elasticsearch import Elasticsearch
import time
from datetime import datetime


# ==================== 配置区域 ====================
# 修改这里的配置来删除不同的字段

# Elasticsearch配置
ES_CONFIG = {
    "host": "http://***************:9200",
    "index_name": "psis-collector-harddisk-index",
    "timeout": 30,
    "max_retries": 3
}

# 要删除的字段列表 - 在这里添加或修改要删除的字段
FIELDS_TO_DELETE = [
    # 示例：tmpfs_runuser相关字段
    # "tmpfs_runuser1009Used",
    # "tmpfs_runuser1009Usage",
    # "tmpfs_runuser1009Size",
    # "tmpfs_runuser1009Avail",
    # "tmpfs_runuser1009Mounted",
    
    # 添加您要删除的字段到这里
    # "field_name_1",
    # "field_name_2",
    # "field_name_3",
]

# 操作配置
OPERATION_CONFIG = {
    "confirm_before_swap": True,  # 是否在交换索引前确认
    "auto_cleanup": True,         # 是否自动清理临时索引
    "show_progress": True,        # 是否显示进度
    "verify_samples": 3           # 验证的样本数量
}

# ==================== 脚本代码 ====================

class ConfigurableESFieldDeleter:
    def __init__(self, config=None):
        """
        初始化可配置的ES字段删除器
        
        Args:
            config (dict): ES配置，如果为None则使用默认配置
        """
        self.config = config or ES_CONFIG
        self.index_name = self.config["index_name"]
        self.temp_index_name = None
        
        # 初始化ES客户端
        try:
            self.es = Elasticsearch(
                [self.config["host"]], 
                request_timeout=self.config.get("timeout", 30),
                max_retries=self.config.get("max_retries", 3),
                retry_on_timeout=True
            )
            if not self.es.ping():
                raise ConnectionError(f"无法连接到Elasticsearch: {self.config['host']}")
            print(f"✓ 成功连接到Elasticsearch: {self.config['host']}")
        except Exception as e:
            raise ConnectionError(f"连接Elasticsearch失败: {e}")

    def delete_fields(self, fields_to_delete, operation_config=None):
        """
        删除指定字段列表
        
        Args:
            fields_to_delete (list): 要删除的字段名列表
            operation_config (dict): 操作配置
            
        Returns:
            bool: 操作是否成功
        """
        if not fields_to_delete:
            print("❌ 字段列表为空，请在配置中添加要删除的字段")
            return False
        
        op_config = operation_config or OPERATION_CONFIG
        self.temp_index_name = f"{self.index_name}_delete_{int(time.time())}"
        
        print(f"开始删除字段操作:")
        print(f"  索引: {self.index_name}")
        print(f"  要删除的字段数量: {len(fields_to_delete)}")
        print(f"  字段列表: {', '.join(fields_to_delete)}")
        print(f"  临时索引: {self.temp_index_name}")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 1. 检查索引状态
            if not self._check_index_status():
                return False
            
            # 2. 分析要删除的字段
            existing_fields = self._analyze_fields(fields_to_delete)
            if not existing_fields:
                return False
            
            # 3. 创建新映射
            new_mapping = self._create_new_mapping(existing_fields)
            if not new_mapping:
                return False
            
            # 4. 创建临时索引
            if not self._create_temp_index(new_mapping):
                return False
            
            # 5. 重新索引数据
            if not self._reindex_data(existing_fields, op_config.get("show_progress", True)):
                return False
            
            # 6. 验证新索引
            if not self._verify_new_index(existing_fields, op_config.get("verify_samples", 3)):
                return False
            
            # 7. 交换索引
            if not self._swap_indices(op_config.get("confirm_before_swap", True)):
                return False
            
            # 8. 最终验证
            self._final_verification(existing_fields)
            
            # 9. 清理（如果启用）
            if op_config.get("auto_cleanup", True):
                self._cleanup_temp_index()
            
            print("=" * 80)
            print("✅ 字段删除操作完成！")
            print(f"已成功删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            if op_config.get("auto_cleanup", True):
                self._cleanup_temp_index()
            return False

    def _check_index_status(self):
        """检查索引状态"""
        print("1. 检查索引状态...")
        
        if not self.es.indices.exists(index=self.index_name):
            print(f"❌ 索引 {self.index_name} 不存在")
            return False
        
        print(f"✓ 索引 {self.index_name} 存在")
        
        # 检查是否是别名
        try:
            aliases = self.es.indices.get_alias(name=self.index_name)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"✓ {self.index_name} 是别名，指向: {actual_index}")
                self.actual_index = actual_index
            else:
                print(f"✓ {self.index_name} 是直接索引")
                self.actual_index = self.index_name
        except:
            self.actual_index = self.index_name
        
        return True

    def _analyze_fields(self, fields_to_delete):
        """分析要删除的字段"""
        print("2. 分析要删除的字段...")
        
        # 获取当前映射
        mapping_response = self.es.indices.get_mapping(index=self.index_name)
        current_mapping = mapping_response[self.actual_index]['mappings']
        properties = current_mapping.get('properties', {})
        
        existing_fields = []
        missing_fields = []
        
        for field in fields_to_delete:
            if field in properties:
                existing_fields.append(field)
                field_type = properties[field].get('type', '未知')
                print(f"✓ 找到字段: {field} (类型: {field_type})")
            else:
                missing_fields.append(field)
                print(f"⚠ 字段不存在: {field}")
        
        if not existing_fields:
            print("❌ 没有找到任何要删除的字段")
            return None
        
        print(f"\n将删除 {len(existing_fields)} 个字段: {', '.join(existing_fields)}")
        if missing_fields:
            print(f"跳过 {len(missing_fields)} 个不存在的字段: {', '.join(missing_fields)}")
        
        # 分析字段使用情况
        print("\n分析字段使用情况...")
        for field in existing_fields:
            try:
                exists_query = {
                    "size": 0,
                    "query": {"exists": {"field": field}}
                }
                result = self.es.search(index=self.index_name, body=exists_query)
                doc_count = result['hits']['total']['value'] if isinstance(result['hits']['total'], dict) else result['hits']['total']
                print(f"  {field}: {doc_count} 个文档包含此字段")
            except Exception as e:
                print(f"  {field}: 无法分析 ({e})")
        
        self.current_mapping = current_mapping
        self.properties = properties
        return existing_fields

    def _create_new_mapping(self, existing_fields):
        """创建新的映射配置"""
        print("\n3. 创建新的映射配置...")
        
        new_mapping = {"properties": {}}
        deleted_count = 0
        kept_count = 0
        
        for field_name, field_config in self.properties.items():
            if field_name in existing_fields:
                print(f"  - 删除字段: {field_name}")
                deleted_count += 1
            else:
                new_mapping['properties'][field_name] = field_config
                kept_count += 1
        
        print(f"✓ 新映射包含 {kept_count} 个字段 (删除了 {deleted_count} 个字段)")
        return new_mapping

    def _create_temp_index(self, new_mapping):
        """创建临时索引"""
        print("\n4. 创建临时索引...")
        
        try:
            # 获取索引设置
            settings_response = self.es.indices.get_settings(index=self.index_name)
            current_settings = settings_response[self.actual_index]['settings']['index']
            
            # 清理设置
            clean_settings = {}
            for key, value in current_settings.items():
                if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                    clean_settings[key] = value
            
            # 创建临时索引
            create_body = {
                "settings": clean_settings,
                "mappings": new_mapping
            }
            
            self.es.indices.create(index=self.temp_index_name, body=create_body)
            print(f"✓ 临时索引 {self.temp_index_name} 创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建临时索引失败: {e}")
            return False

    def _reindex_data(self, existing_fields, show_progress=True):
        """重新索引数据"""
        print("\n5. 重新索引数据...")
        
        try:
            # 构建删除字段的脚本
            script_lines = []
            for field in existing_fields:
                script_lines.append(f'if (ctx._source.containsKey("{field}")) {{ ctx._source.remove("{field}"); }}')
            
            script_source = "\n".join(script_lines)
            
            reindex_body = {
                "source": {"index": self.index_name},
                "dest": {"index": self.temp_index_name},
                "script": {
                    "source": script_source,
                    "lang": "painless"
                }
            }
            
            # 启动异步重新索引
            result = self.es.reindex(body=reindex_body, wait_for_completion=False, timeout="60m")
            task_id = result['task']
            print(f"✓ 数据迁移任务已启动，任务ID: {task_id}")
            
            # 监控进度
            if show_progress:
                print("监控数据迁移进度...")
                while True:
                    try:
                        task_status = self.es.tasks.get(task_id=task_id)
                        
                        if task_status['completed']:
                            print("✓ 数据迁移任务完成")
                            response = task_status.get('response', {})
                            print(f"  处理文档数: {response.get('total', 0)}")
                            print(f"  创建文档数: {response.get('created', 0)}")
                            if response.get('failures'):
                                print(f"  失败记录: {len(response['failures'])}")
                            break
                        else:
                            task_info = task_status['task']
                            status = task_info.get('status', {})
                            total = status.get('total', 0)
                            created = status.get('created', 0)
                            if total > 0:
                                progress = (created / total) * 100
                                print(f"  进度: {created}/{total} ({progress:.1f}%)")
                            else:
                                print(f"  已处理: {created} 条记录")
                            
                            time.sleep(5)
                            
                    except Exception as task_error:
                        print(f"⚠ 获取任务状态失败: {task_error}")
                        time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ 重新索引失败: {e}")
            return False

    def _verify_new_index(self, existing_fields, sample_count=3):
        """验证新索引"""
        print("\n6. 验证新索引...")
        
        try:
            # 检查文档数量
            old_count = self.es.count(index=self.index_name)['count']
            new_count = self.es.count(index=self.temp_index_name)['count']
            
            print(f"  原索引文档数: {old_count}")
            print(f"  新索引文档数: {new_count}")
            
            if old_count == new_count:
                print("✅ 文档数量验证通过")
            else:
                print(f"⚠ 文档数量不匹配，差异: {old_count - new_count}")
                user_continue = input("文档数量不匹配，是否继续？(y/N): ")
                if user_continue.lower() != 'y':
                    return False
            
            # 检查字段是否已删除
            new_mapping_check = self.es.indices.get_mapping(index=self.temp_index_name)
            new_properties = new_mapping_check[self.temp_index_name]['mappings']['properties']
            
            print("字段删除验证:")
            all_deleted = True
            for field in existing_fields:
                if field in new_properties:
                    print(f"  ❌ {field} 仍然存在（删除失败）")
                    all_deleted = False
                else:
                    print(f"  ✅ {field} 已成功删除")
            
            if not all_deleted:
                print("❌ 部分字段删除失败")
                return False
            
            # 检查样本数据
            print(f"\n样本数据验证 (检查 {sample_count} 个样本):")
            search_body = {
                "size": sample_count,
                "_source": existing_fields + ["disk_lsof_tmp_delete"],
                "query": {"match_all": {}}
            }
            
            try:
                new_response = self.es.search(index=self.temp_index_name, body=search_body)
                
                for i, hit in enumerate(new_response['hits']['hits'], 1):
                    print(f"  {i}. 文档ID: {hit['_id']}")
                    source = hit['_source']
                    
                    # 检查删除的字段
                    deleted_fields_found = [field for field in existing_fields if field in source]
                    
                    if deleted_fields_found:
                        print(f"     ❌ 仍包含已删除字段: {', '.join(deleted_fields_found)}")
                        all_deleted = False
                    else:
                        print(f"     ✅ 已删除字段不存在")
                    
                    # 检查保留字段
                    if 'disk_lsof_tmp_delete' in source:
                        print(f"     ✅ 保留字段 disk_lsof_tmp_delete: {source['disk_lsof_tmp_delete']}")
                        
            except Exception as search_error:
                print(f"⚠ 样本数据验证失败: {search_error}")
            
            return all_deleted
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

    def _swap_indices(self, confirm_before_swap=True):
        """交换索引"""
        print("\n7. 交换索引...")
        
        try:
            if confirm_before_swap:
                # 显示摘要
                print("验证摘要:")
                new_mapping_check = self.es.indices.get_mapping(index=self.temp_index_name)
                new_properties = new_mapping_check[self.temp_index_name]['mappings']['properties']
                old_count = self.es.count(index=self.index_name)['count']
                new_count = self.es.count(index=self.temp_index_name)['count']
                
                print(f"  - 保留字段数: {len(new_properties)}")
                print(f"  - 文档数变化: {old_count} -> {new_count}")
                
                user_input = input("\n是否用新索引替换原索引？(y/N): ")
                if user_input.lower() != 'y':
                    print(f"操作已取消，临时索引保留为: {self.temp_index_name}")
                    return True
            
            # 删除原索引或别名
            if hasattr(self, 'actual_index') and self.actual_index != self.index_name:
                # 是别名
                self.es.indices.delete_alias(index=self.actual_index, name=self.index_name)
                print(f"✓ 已删除别名 {self.index_name}")
                self.es.indices.delete(index=self.actual_index)
                print(f"✓ 已删除原索引 {self.actual_index}")
            else:
                # 是直接索引
                self.es.indices.delete(index=self.index_name)
                print(f"✓ 已删除原索引 {self.index_name}")

            # 真正重命名临时索引：创建新索引并迁移数据
            print(f"正在将临时索引重命名为原索引名...")
            self._rename_temp_to_final()
            print(f"✓ 已将临时索引重命名为 {self.index_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ 交换索引失败: {e}")
            return False

    def _rename_temp_to_final(self):
        """将临时索引真正重命名为最终索引名"""
        try:
            # 获取临时索引的设置和映射
            temp_settings_response = self.es.indices.get_settings(index=self.temp_index_name)
            temp_settings = temp_settings_response[self.temp_index_name]['settings']['index']

            temp_mapping_response = self.es.indices.get_mapping(index=self.temp_index_name)
            temp_mapping = temp_mapping_response[self.temp_index_name]['mappings']

            # 清理设置
            clean_settings = {}
            for key, value in temp_settings.items():
                if key not in ['creation_date', 'uuid', 'version', 'provided_name']:
                    clean_settings[key] = value

            # 创建最终索引
            create_body = {
                "settings": clean_settings,
                "mappings": temp_mapping
            }

            self.es.indices.create(index=self.index_name, body=create_body)

            # 重新索引数据从临时索引到最终索引
            reindex_body = {
                "source": {"index": self.temp_index_name},
                "dest": {"index": self.index_name}
            }

            result = self.es.reindex(body=reindex_body, wait_for_completion=True, timeout="30m")

            # 删除临时索引
            self.es.indices.delete(index=self.temp_index_name)

            return True

        except Exception as e:
            print(f"❌ 重命名临时索引失败: {e}")
            return False

    def _final_verification(self, existing_fields):
        """最终验证"""
        print("\n8. 最终验证...")
        
        try:
            final_mapping = self.es.indices.get_mapping(index=self.index_name)
            final_actual_index = list(final_mapping.keys())[0]
            final_properties = final_mapping[final_actual_index]['mappings']['properties']
            
            print("最终字段状态:")
            for field in existing_fields:
                if field in final_properties:
                    print(f"  ❌ {field} 仍然存在")
                else:
                    print(f"  ✅ {field} 已成功删除")
            
            # 检查重要字段
            if 'disk_lsof_tmp_delete' in final_properties:
                field_type = final_properties['disk_lsof_tmp_delete'].get('type', '未知')
                print(f"  ✅ disk_lsof_tmp_delete 保留，类型: {field_type}")
            
            final_count = self.es.count(index=self.index_name)['count']
            print(f"✅ 最终文档数: {final_count}")
            print(f"✅ 最终字段数: {len(final_properties)}")
            
        except Exception as e:
            print(f"⚠ 最终验证失败: {e}")

    def _cleanup_temp_index(self):
        """清理临时索引"""
        try:
            if self.temp_index_name and self.es.indices.exists(index=self.temp_index_name):
                self.es.indices.delete(index=self.temp_index_name)
                print(f"✓ 已清理临时索引 {self.temp_index_name}")
        except Exception as e:
            print(f"⚠ 清理临时索引失败: {e}")


def show_all_fields():
    """显示索引中的所有字段"""
    try:
        print("\n🔍 正在获取索引字段信息...")

        # 连接ES
        es = Elasticsearch([ES_CONFIG["host"]], timeout=30)
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return

        index_name = ES_CONFIG["index_name"]

        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            print(f"❌ 索引 {index_name} 不存在")
            return

        # 获取映射
        mapping_response = es.indices.get_mapping(index=index_name)

        # 处理别名情况
        actual_index = list(mapping_response.keys())[0]
        properties = mapping_response[actual_index]['mappings'].get('properties', {})

        if not properties:
            print("❌ 索引中没有字段")
            return

        print(f"\n📋 索引 {index_name} 中的所有字段 (共 {len(properties)} 个):")
        print("-" * 80)

        # 按字段名排序
        sorted_fields = sorted(properties.items())

        for i, (field_name, field_config) in enumerate(sorted_fields, 1):
            field_type = field_config.get('type', '未知')
            print(f"{i:3d}. {field_name:<40} (类型: {field_type})")

        print("-" * 80)
        print(f"总计: {len(properties)} 个字段")

        # 提供搜索功能
        print("\n🔍 可以按关键词搜索字段:")
        search_term = input("输入搜索关键词（回车跳过）: ").strip()

        if search_term:
            matching_fields = [name for name in properties.keys() if search_term.lower() in name.lower()]
            if matching_fields:
                print(f"\n包含 '{search_term}' 的字段 (共 {len(matching_fields)} 个):")
                for i, field_name in enumerate(matching_fields, 1):
                    field_type = properties[field_name].get('type', '未知')
                    print(f"{i:3d}. {field_name:<40} (类型: {field_type})")

                # 询问是否使用搜索结果
                use_search = input(f"\n是否删除这 {len(matching_fields)} 个匹配的字段？(y/N): ").strip().lower()
                if use_search == 'y':
                    return matching_fields
            else:
                print(f"❌ 没有找到包含 '{search_term}' 的字段")

        input("\n按回车键继续...")

    except Exception as e:
        print(f"❌ 获取字段信息失败: {e}")


def get_fields_from_user():
    """从用户输入获取要删除的字段列表"""
    print("📝 请输入要删除的字段")
    print("=" * 60)
    print("支持以下输入方式:")
    print("1. 逐个输入字段名（每行一个，输入空行结束）")
    print("2. 一次性输入多个字段（用逗号分隔）")
    print("3. 使用配置文件中的预设字段")
    print("4. 查看索引中的所有字段")
    print("-" * 60)

    # 显示配置文件中的预设字段
    if FIELDS_TO_DELETE:
        print("配置文件中的预设字段:")
        for i, field in enumerate(FIELDS_TO_DELETE, 1):
            print(f"  {i}. {field}")
        print()
        use_preset = input("是否使用配置文件中的预设字段？(y/N): ").strip().lower()
        if use_preset == 'y':
            return FIELDS_TO_DELETE

    print("\n请选择操作:")
    print("1. 逐个输入字段名")
    print("2. 一次性输入多个字段（用逗号分隔）")
    print("3. 查看索引中的所有字段")
    print("0. 退出")

    while True:
        choice = input("请选择 (0-3): ").strip()
        if choice in ['0', '1', '2', '3']:
            break
        print("❌ 请输入 0-3")

    if choice == '0':
        print("👋 退出")
        return None

    if choice == '3':
        # 查看所有字段
        show_all_fields()
        return get_fields_from_user()  # 递归调用，让用户重新选择

    fields = []

    if choice == '1':
        # 逐个输入
        print("\n请逐个输入字段名（输入空行结束）:")
        while True:
            field = input("字段名: ").strip()
            if not field:
                break
            if field in fields:
                print(f"⚠ 字段 {field} 已存在，跳过")
                continue
            fields.append(field)
            print(f"✓ 已添加字段: {field}")

    else:
        # 一次性输入
        print("\n请输入要删除的字段（用逗号分隔）:")
        print("示例: field1,field2,field3")
        print("或者: field1, field2, field3")

        while True:
            input_text = input("字段列表: ").strip()
            if input_text:
                # 分割字段并清理空白
                raw_fields = [f.strip() for f in input_text.split(',')]
                fields = [f for f in raw_fields if f]  # 过滤空字符串

                if fields:
                    break
                else:
                    print("❌ 没有有效的字段名，请重新输入")
            else:
                print("❌ 输入不能为空，请重新输入")

    if not fields:
        print("❌ 没有输入任何字段")
        return None

    # 确认字段列表
    print(f"\n📋 确认要删除的字段 (共 {len(fields)} 个):")
    for i, field in enumerate(fields, 1):
        print(f"  {i}. {field}")

    confirm = input(f"\n是否确认删除以上 {len(fields)} 个字段？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消")
        return None

    return fields


def main():
    """主函数"""
    try:
        # 获取要删除的字段列表
        fields_to_delete = get_fields_from_user()
        if not fields_to_delete:
            return 1

        # 创建字段删除器
        deleter = ConfigurableESFieldDeleter(ES_CONFIG)

        # 执行删除操作
        success = deleter.delete_fields(fields_to_delete, OPERATION_CONFIG)

        if success:
            print("\n🎉 所有操作成功完成！")
            return 0
        else:
            print("\n❌ 操作失败")
            return 1

    except KeyboardInterrupt:
        print("\n\n👋 用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
