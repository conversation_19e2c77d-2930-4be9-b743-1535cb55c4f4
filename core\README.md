# 核心功能脚本

这个目录包含主要的功能脚本，是日常使用的核心工具。

## 文件说明

### delete_fields_config.py 🌟 **主要使用**
- **功能**: 删除Elasticsearch索引中的指定字段
- **特点**: 可配置，只需修改字段列表即可使用
- **使用**: `python delete_fields_config.py`

### es_field_type_converter.py
- **功能**: 转换字段类型（如text转integer）
- **特点**: 完整的类型转换功能，包含数据转换逻辑
- **使用**: `python es_field_type_converter.py`

## 使用建议

1. **删除字段**: 优先使用 `delete_fields_config.py`
2. **类型转换**: 使用 `es_field_type_converter.py`
3. **操作前**: 建议先运行验证脚本检查状态
