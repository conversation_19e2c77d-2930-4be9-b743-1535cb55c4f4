#!/usr/bin/env python3
"""
验证指定字段是否已被删除
"""

from elasticsearch import Elasticsearch


def verify_deleted_fields():
    """验证字段删除结果"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    
    # 应该被删除的字段列表
    DELETED_FIELDS = [
        "tmpfs_runuser500Used",
        "tmpfs_runuser500Usage", 
        "tmpfs_runuser500Size",
        "tmpfs_runuser500Avail",
        "tmpfs_runuser500Mounted"
    ]
    
    print(f"验证字段删除结果:")
    print(f"  索引: {INDEX_NAME}")
    print(f"  检查字段: {', '.join(DELETED_FIELDS)}")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查索引状态
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return False
        
        print(f"✓ 索引 {INDEX_NAME} 存在")
        
        # 检查是否是别名
        try:
            aliases = es.indices.get_alias(name=INDEX_NAME)
            if aliases:
                actual_index = list(aliases.keys())[0]
                print(f"✓ {INDEX_NAME} 是别名，指向: {actual_index}")
            else:
                actual_index = INDEX_NAME
                print(f"✓ {INDEX_NAME} 是直接索引")
        except:
            actual_index = INDEX_NAME
        
        # 2. 获取当前索引映射
        print("\n检查索引映射...")
        mapping_response = es.indices.get_mapping(index=INDEX_NAME)
        current_mapping = mapping_response[actual_index]['mappings']
        properties = current_mapping.get('properties', {})
        
        print(f"索引总字段数: {len(properties)}")
        
        # 3. 检查每个应该被删除的字段
        print("\n字段删除验证:")
        all_deleted = True
        
        for field in DELETED_FIELDS:
            if field in properties:
                field_type = properties[field].get('type', '未知')
                print(f"  ❌ {field} 仍然存在 (类型: {field_type})")
                all_deleted = False
            else:
                print(f"  ✅ {field} 已成功删除")
        
        # 4. 检查数据中是否还有这些字段
        print("\n检查文档数据...")
        for field in DELETED_FIELDS:
            try:
                # 搜索包含该字段的文档
                exists_query = {
                    "size": 0,
                    "query": {
                        "exists": {
                            "field": field
                        }
                    }
                }
                
                result = es.search(index=INDEX_NAME, body=exists_query)
                doc_count = result['hits']['total']['value'] if isinstance(result['hits']['total'], dict) else result['hits']['total']
                
                if doc_count > 0:
                    print(f"  ⚠ {field}: 仍有 {doc_count} 个文档包含此字段")
                    all_deleted = False
                else:
                    print(f"  ✅ {field}: 没有文档包含此字段")
                    
            except Exception as e:
                print(f"  ✅ {field}: 查询失败 (字段可能已删除) - {e}")
        
        # 5. 检查保留的重要字段
        print("\n检查保留字段:")
        important_fields = ["disk_lsof_tmp_delete", "IP", "Date", "NeName"]
        
        for field in important_fields:
            if field in properties:
                field_type = properties[field].get('type', '未知')
                print(f"  ✅ {field} 保留 (类型: {field_type})")
            else:
                print(f"  ⚠ {field} 不存在")
        
        # 6. 获取样本数据验证
        print("\n样本数据验证:")
        search_body = {
            "size": 3,
            "_source": DELETED_FIELDS + ["disk_lsof_tmp_delete"],
            "query": {"match_all": {}}
        }
        
        try:
            response = es.search(index=INDEX_NAME, body=search_body)
            
            for i, hit in enumerate(response['hits']['hits'], 1):
                print(f"  {i}. 文档ID: {hit['_id']}")
                source = hit['_source']
                
                # 检查删除的字段
                found_deleted_fields = []
                for field in DELETED_FIELDS:
                    if field in source:
                        found_deleted_fields.append(field)
                
                if found_deleted_fields:
                    print(f"     ❌ 仍包含已删除字段: {', '.join(found_deleted_fields)}")
                    all_deleted = False
                else:
                    print(f"     ✅ 不包含已删除字段")
                
                # 检查保留字段
                if 'disk_lsof_tmp_delete' in source:
                    print(f"     ✅ 保留字段 disk_lsof_tmp_delete: {source['disk_lsof_tmp_delete']}")
                else:
                    print(f"     ⚠ 保留字段 disk_lsof_tmp_delete 不存在")
                
        except Exception as search_error:
            print(f"⚠ 样本数据验证失败: {search_error}")
        
        # 7. 总结
        print("\n" + "=" * 80)
        if all_deleted:
            print("✅ 所有指定字段已成功删除！")
            
            # 显示索引统计
            total_docs = es.count(index=INDEX_NAME)['count']
            print(f"✅ 索引文档总数: {total_docs}")
            print(f"✅ 索引字段总数: {len(properties)}")
            
        else:
            print("❌ 部分字段删除失败或仍存在于数据中")
        
        return all_deleted
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


if __name__ == "__main__":
    verify_deleted_fields()
