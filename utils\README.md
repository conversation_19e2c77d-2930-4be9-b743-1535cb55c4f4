# 工具和辅助脚本

这个目录包含各种工具脚本，用于检查、诊断和维护操作。

## 文件说明

### check_fields.py
- **功能**: 检查索引中字段的详细信息
- **用途**: 分析字段类型、使用情况等
- **使用**: `python check_fields.py`

### check_index_status.py
- **功能**: 检查和修复索引状态
- **用途**: 解决索引别名问题，清理临时索引
- **使用**: `python check_index_status.py`

### rename_index_interactive.py
- **功能**: 交互式索引重命名
- **用途**: 将任意索引重命名为指定名称
- **特点**: 支持用户输入当前索引名和目标索引名
- **使用**: `python rename_index_interactive.py`

## 使用场景

- **问题诊断**: 使用check_*脚本
- **状态修复**: 索引名称或别名问题
- **信息查询**: 了解字段和索引详情
