#!/usr/bin/env python3
"""
检查当前索引状态并修复别名问题
"""

from elasticsearch import Elasticsearch


def check_and_fix_index_status():
    """检查索引状态并修复"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    TARGET_INDEX_NAME = "psis-collector-harddisk-index"
    
    print(f"检查索引状态...")
    print("=" * 80)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 列出所有相关索引
        print("\n查找相关索引...")
        all_indices = es.indices.get(index="*")
        
        related_indices = []
        for index_name in all_indices.keys():
            if "psis-collector-harddisk-index" in index_name:
                related_indices.append(index_name)
        
        print(f"找到 {len(related_indices)} 个相关索引:")
        for index in related_indices:
            doc_count = es.count(index=index)['count']
            print(f"  - {index}: {doc_count} 条文档")
        
        # 2. 检查目标索引是否存在
        target_exists = es.indices.exists(index=TARGET_INDEX_NAME)
        print(f"\n目标索引 {TARGET_INDEX_NAME} 存在: {target_exists}")
        
        # 3. 检查别名
        print("\n检查别名...")
        try:
            aliases = es.indices.get_alias(name=TARGET_INDEX_NAME)
            if aliases:
                print(f"✓ 别名 {TARGET_INDEX_NAME} 存在，指向:")
                for actual_index in aliases.keys():
                    print(f"  - {actual_index}")
            else:
                print(f"⚠ 别名 {TARGET_INDEX_NAME} 不存在")
        except Exception as alias_error:
            print(f"⚠ 检查别名失败: {alias_error}")
        
        # 4. 找到最新的索引（应该是包含完整数据的）
        print("\n分析索引...")
        best_index = None
        max_docs = 0
        
        for index in related_indices:
            try:
                doc_count = es.count(index=index)['count']
                
                # 检查是否包含重要字段
                mapping = es.indices.get_mapping(index=index)
                properties = mapping[index]['mappings'].get('properties', {})
                
                has_important_field = 'disk_lsof_tmp_delete' in properties
                field_count = len(properties)
                
                print(f"  {index}:")
                print(f"    - 文档数: {doc_count}")
                print(f"    - 字段数: {field_count}")
                print(f"    - 包含disk_lsof_tmp_delete: {has_important_field}")
                
                # 选择文档数最多且包含重要字段的索引
                if has_important_field and doc_count > max_docs:
                    max_docs = doc_count
                    best_index = index
                    
            except Exception as e:
                print(f"    - 检查失败: {e}")
        
        if best_index:
            print(f"\n✓ 推荐使用索引: {best_index} ({max_docs} 条文档)")
            
            # 5. 修复别名
            if best_index != TARGET_INDEX_NAME:
                print(f"\n修复别名...")
                
                # 删除现有别名（如果存在）
                try:
                    existing_aliases = es.indices.get_alias(name=TARGET_INDEX_NAME)
                    for existing_index in existing_aliases.keys():
                        es.indices.delete_alias(index=existing_index, name=TARGET_INDEX_NAME)
                        print(f"✓ 已删除 {existing_index} 的别名 {TARGET_INDEX_NAME}")
                except:
                    pass
                
                # 删除目标索引（如果存在且是实际索引）
                if target_exists:
                    try:
                        # 检查是否是别名
                        try:
                            es.indices.get_alias(name=TARGET_INDEX_NAME)
                            # 如果没有异常，说明是别名，不需要删除
                        except:
                            # 如果有异常，说明是实际索引，需要删除
                            es.indices.delete(index=TARGET_INDEX_NAME)
                            print(f"✓ 已删除实际索引 {TARGET_INDEX_NAME}")
                    except Exception as del_error:
                        print(f"⚠ 删除索引失败: {del_error}")
                
                # 创建新别名
                es.indices.put_alias(index=best_index, name=TARGET_INDEX_NAME)
                print(f"✓ 已将 {best_index} 设置别名为 {TARGET_INDEX_NAME}")
                
                # 6. 验证修复结果
                print("\n验证修复结果...")
                
                # 检查别名
                try:
                    new_aliases = es.indices.get_alias(name=TARGET_INDEX_NAME)
                    if new_aliases:
                        actual_index = list(new_aliases.keys())[0]
                        print(f"✅ 别名 {TARGET_INDEX_NAME} 现在指向: {actual_index}")
                        
                        # 检查数据
                        doc_count = es.count(index=TARGET_INDEX_NAME)['count']
                        print(f"✅ 可访问文档数: {doc_count}")
                        
                        # 检查重要字段
                        mapping = es.indices.get_mapping(index=TARGET_INDEX_NAME)
                        properties = mapping[actual_index]['mappings'].get('properties', {})
                        
                        if 'disk_lsof_tmp_delete' in properties:
                            field_type = properties['disk_lsof_tmp_delete'].get('type', '未知')
                            print(f"✅ disk_lsof_tmp_delete 字段存在，类型: {field_type}")
                        
                        print(f"✅ 总字段数: {len(properties)}")
                        
                    else:
                        print("❌ 别名设置失败")
                        return False
                        
                except Exception as verify_error:
                    print(f"❌ 验证失败: {verify_error}")
                    return False
            else:
                print(f"✓ 索引 {TARGET_INDEX_NAME} 已经是正确的")
        else:
            print("❌ 没有找到合适的索引")
            return False
        
        # 7. 清理建议
        print("\n清理建议:")
        cleanup_indices = []
        for index in related_indices:
            if index != best_index and index != TARGET_INDEX_NAME:
                cleanup_indices.append(index)
        
        if cleanup_indices:
            print("可以删除的临时索引:")
            for index in cleanup_indices:
                doc_count = es.count(index=index)['count']
                print(f"  - {index} ({doc_count} 条文档)")
            
            user_input = input(f"\n是否删除这些临时索引？(y/N): ")
            if user_input.lower() == 'y':
                for index in cleanup_indices:
                    try:
                        es.indices.delete(index=index)
                        print(f"✓ 已删除 {index}")
                    except Exception as del_error:
                        print(f"⚠ 删除 {index} 失败: {del_error}")
        else:
            print("没有需要清理的临时索引")
        
        print("\n" + "=" * 80)
        print("✅ 索引状态检查和修复完成！")
        print(f"现在可以正常使用索引: {TARGET_INDEX_NAME}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False


if __name__ == "__main__":
    check_and_fix_index_status()
