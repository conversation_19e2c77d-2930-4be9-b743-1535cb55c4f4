#!/usr/bin/env python3
"""
验证删除字段操作是否正确保持索引名不变
"""

from elasticsearch import Elasticsearch
import time


def verify_delete_operation():
    """验证删除字段操作"""
    
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    
    print("🔍 验证删除字段操作")
    print("=" * 60)
    
    try:
        # 连接ES
        es = Elasticsearch([ES_HOST])
        
        if not es.ping():
            print("❌ 无法连接到Elasticsearch")
            return False
        
        print("✓ 成功连接到Elasticsearch")
        
        # 1. 检查索引状态
        print("\n1. 检查索引状态...")
        
        if not es.indices.exists(index=INDEX_NAME):
            print(f"❌ 索引 {INDEX_NAME} 不存在")
            return False
        
        print(f"✓ 索引 {INDEX_NAME} 存在")
        
        # 检查是否是别名
        is_alias = False
        actual_index = INDEX_NAME
        try:
            aliases = es.indices.get_alias(name=INDEX_NAME)
            if aliases:
                is_alias = True
                actual_index = list(aliases.keys())[0]
                print(f"⚠ {INDEX_NAME} 是别名，指向: {actual_index}")
            else:
                print(f"✅ {INDEX_NAME} 是直接索引")
        except:
            print(f"✅ {INDEX_NAME} 是直接索引")
        
        # 2. 检查索引基本信息
        print("\n2. 检查索引基本信息...")
        
        # 文档数量
        doc_count = es.count(index=INDEX_NAME)['count']
        print(f"✓ 文档总数: {doc_count:,}")
        
        # 字段数量
        mapping = es.indices.get_mapping(index=INDEX_NAME)
        if is_alias:
            properties = mapping[actual_index]['mappings'].get('properties', {})
        else:
            properties = mapping[INDEX_NAME]['mappings'].get('properties', {})
        
        field_count = len(properties)
        print(f"✓ 字段总数: {field_count}")
        
        # 3. 检查重要字段
        print("\n3. 检查重要字段...")
        
        important_fields = {
            "disk_lsof_tmp_delete": "integer",
            "IP": "text", 
            "Date": "date",
            "NeName": "text"
        }
        
        all_good = True
        for field, expected_type in important_fields.items():
            if field in properties:
                actual_type = properties[field].get('type', '未知')
                if actual_type == expected_type:
                    print(f"✅ {field}: {actual_type}")
                else:
                    print(f"⚠ {field}: 期望 {expected_type}，实际 {actual_type}")
                    all_good = False
            else:
                print(f"❌ {field}: 不存在")
                all_good = False
        
        # 4. 检查是否有临时索引残留
        print("\n4. 检查临时索引残留...")
        
        all_indices = es.indices.get(index="*")
        temp_indices = []
        
        for index_name in all_indices.keys():
            if ("psis-collector-harddisk-index" in index_name and 
                index_name != INDEX_NAME and
                ("_temp_" in index_name or "_delete_" in index_name or "_rename_" in index_name)):
                temp_indices.append(index_name)
        
        if temp_indices:
            print(f"⚠ 发现 {len(temp_indices)} 个临时索引:")
            for temp_index in temp_indices:
                doc_count = es.count(index=temp_index)['count']
                print(f"  - {temp_index}: {doc_count:,} 条文档")
            all_good = False
        else:
            print("✅ 没有发现临时索引残留")
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("📊 验证结果总结:")
        
        if is_alias:
            if "_temp_" in actual_index or "_delete_" in actual_index or "_rename_" in actual_index:
                print("❌ 索引名称有问题：使用了临时名称的别名")
                print(f"   当前: {INDEX_NAME} -> {actual_index}")
                print("   建议: 使用重命名工具修复")
                all_good = False
            else:
                print("⚠ 索引使用别名（可接受但不是最佳实践）")
                print(f"   别名: {INDEX_NAME} -> {actual_index}")
        else:
            print("✅ 索引名称正确（直接索引）")
            print(f"   索引: {INDEX_NAME}")
        
        if all_good:
            print("✅ 所有检查项目通过")
            print("✅ 删除字段操作正确保持了索引名不变")
        else:
            print("⚠ 发现一些问题，需要修复")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    success = verify_delete_operation()
    
    if success:
        print("\n🎉 验证通过！删除字段操作工作正常！")
        return 0
    else:
        print("\n⚠ 验证发现问题，需要检查和修复")
        print("\n🔧 修复建议:")
        print("1. 如果索引名有问题: python utils/rename_index_interactive.py")
        print("2. 如果有临时索引残留: 手动删除临时索引")
        print("3. 如果字段有问题: 检查字段删除或转换操作")
        return 1


if __name__ == "__main__":
    exit(main())
