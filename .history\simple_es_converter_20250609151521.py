#!/usr/bin/env python3
"""
简化版Elasticsearch字段类型转换脚本
快速将disk_lsof_tmp_deletey字段从text转换为integer
"""

from elasticsearch import Elasticsearch
import json


def convert_field_type():
    """执行字段类型转换"""
    
    # ES配置
    ES_HOST = "http://***************:9200"
    INDEX_NAME = "psis-collector-harddisk-index"
    FIELD_NAME = "disk_lsof_tmp_deletey"
    TEMP_INDEX = f"{INDEX_NAME}_temp"
    
    # 连接ES
    print(f"连接到 {ES_HOST}...")
    es = Elasticsearch([ES_HOST])
    
    if not es.ping():
        print("❌ 无法连接到Elasticsearch")
        return False
    
    print("✓ 连接成功")
    
    try:
        # 1. 获取原索引映射
        print("获取原索引映射...")
        original_mapping = es.indices.get_mapping(index=INDEX_NAME)
        mapping = original_mapping[INDEX_NAME]['mappings']
        
        # 2. 修改字段类型
        print(f"修改字段 {FIELD_NAME} 类型为 integer...")
        if 'properties' not in mapping:
            mapping['properties'] = {}
        
        mapping['properties'][FIELD_NAME] = {
            "type": "integer",
            "null_value": 0
        }
        
        # 3. 创建临时索引
        print(f"创建临时索引 {TEMP_INDEX}...")
        
        # 获取原索引设置
        settings = es.indices.get_settings(index=INDEX_NAME)
        index_settings = settings[INDEX_NAME]['settings']['index']
        
        # 清理设置
        clean_settings = {k: v for k, v in index_settings.items() 
                         if k not in ['creation_date', 'uuid', 'version', 'provided_name']}
        
        # 创建临时索引
        es.indices.create(
            index=TEMP_INDEX,
            body={
                "settings": clean_settings,
                "mappings": mapping
            }
        )
        
        # 4. 重新索引数据
        print("重新索引数据...")
        reindex_body = {
            "source": {"index": INDEX_NAME},
            "dest": {"index": TEMP_INDEX},
            "script": {
                "source": f"""
                if (ctx._source.{FIELD_NAME} != null) {{
                    try {{
                        ctx._source.{FIELD_NAME} = Integer.parseInt(ctx._source.{FIELD_NAME}.toString());
                    }} catch (NumberFormatException e) {{
                        ctx._source.{FIELD_NAME} = 0;
                    }}
                }} else {{
                    ctx._source.{FIELD_NAME} = 0;
                }}
                """,
                "lang": "painless"
            }
        }
        
        result = es.reindex(body=reindex_body, wait_for_completion=True)
        print(f"✓ 重新索引完成，处理 {result.get('total', 0)} 条记录")
        
        # 5. 交换索引
        print("交换索引...")
        es.indices.delete(index=INDEX_NAME)
        es.indices.put_alias(index=TEMP_INDEX, name=INDEX_NAME)
        
        # 6. 验证
        print("验证结果...")
        new_mapping = es.indices.get_mapping(index=INDEX_NAME)
        field_type = new_mapping[INDEX_NAME]['mappings']['properties'][FIELD_NAME]['type']
        print(f"✓ 字段 {FIELD_NAME} 当前类型: {field_type}")
        
        print("✅ 转换完成！")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        # 清理临时索引
        try:
            if es.indices.exists(index=TEMP_INDEX):
                es.indices.delete(index=TEMP_INDEX)
                print(f"已清理临时索引 {TEMP_INDEX}")
        except:
            pass
        return False


if __name__ == "__main__":
    convert_field_type()
